<template>
  <page-meta
    :page-style="
      'overflow:' + (showDetailPopup ? 'hidden' : 'visible')
    "
  ></page-meta>
  <page-container
    :show="showDetailPopup"
    @beforeleave="showDetailPopup = false"
  ></page-container>
  <view class="workbench-wrap">
    <!-- 结算金额统计卡片 -->
    <view class="summary-card">
      <view class="summary-title">结算概况</view>
      <view class="summary-stats">
        <!-- <view class="stat-item total">
          <view class="stat-label">全部金额</view>
          <view class="stat-value">¥{{ summary.totalAmount }}</view>
        </view> -->
        <view class="stat-item pending">
          <view class="stat-label">待结算</view>
          <view class="stat-value">¥{{ summary.pendingAmount }}</view>
        </view>
        <view class="stat-item processing">
          <view class="stat-label">结算中</view>
          <view class="stat-value">¥{{ summary.processingAmount }}</view>
        </view>
        <view class="stat-item finished">
          <view class="stat-label">已结算</view>
          <view class="stat-value">¥{{ summary.settledAmount }}</view>
        </view>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <u-search
          shape="square"
          v-model="keyWords"
          placeholder="搜索任务名称、客户名称、联系人"
          :showAction="true"
          actionText="搜索"
          @search="handleSearch"
          @custom="handleSearch"
        ></u-search>
      </view>
      <!-- 筛选按钮 -->
      <!-- <view class="filter-btn" @click="showFilterPopup = true">
        <u-icon name="list" size="20" color="#666"></u-icon>
        <text>筛选</text>
      </view> -->
    </view>

    <!-- 结算状态tab栏 -->
    <view class="order-tab-bar">
      <view
        v-for="(tab, idx) in tabList"
        :key="tab.value"
        :class="['tab-item', { active: currentTab === idx }]"
        @tap="handleTabChange(idx)"
      >
        {{ tab.label }}
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <u-popup
      :show="showFilterPopup"
      mode="bottom"
      @close="showFilterPopup = false"
      :closeable="true"
    >
      <view class="filter-popup">
        <view class="popup-title">筛选条件</view>
        <view class="filter-content">
          <view class="filter-item">
            <view class="filter-label">金额范围</view>
            <view class="amount-range">
              <u-input
                v-model="filterForm.minAmount"
                placeholder="最小金额"
                type="number"
              />
              <text style="margin: 0 10rpx;">至</text>
              <u-input
                v-model="filterForm.maxAmount"
                placeholder="最大金额"
                type="number"
              />
            </view>
          </view>
          <view class="filter-item">
            <view class="filter-label">结算时间</view>
            <view class="date-range">
              <u-input
                v-model="filterForm.startDate"
                placeholder="开始日期"
                type="select"
                @click="showStartDatePicker = true"
              />
              <text style="margin: 0 10rpx;">至</text>
              <u-input
                v-model="filterForm.endDate"
                placeholder="结束日期"
                type="select"
                @click="showEndDatePicker = true"
              />
            </view>
          </view>
        </view>
        <view class="filter-footer">
          <u-button type="info" plain @click="resetFilter">重置</u-button>
          <u-button type="primary" @click="applyFilter">确定</u-button>
        </view>
      </view>
    </u-popup>

    <!-- 日期选择器 -->
    <u-datetime-picker
      v-model="filterForm.startDate"
      :show="showStartDatePicker"
      @cancel="showStartDatePicker = false"
      @confirm="showStartDatePicker = false"
      mode="date"
    ></u-datetime-picker>
    <u-datetime-picker
      v-model="filterForm.endDate"
      :show="showEndDatePicker"
      @cancel="showEndDatePicker = false"
      @confirm="showEndDatePicker = false"
      mode="date"
    ></u-datetime-picker>

    <!-- 任务列表 -->
    <view class="order-list">
      <view v-if="list.length === 0" class="empty-tip"> 暂无结算任务 </view>
      <view
        v-for="item in list"
        @click.stop="showOrderDetail(item)"
        :key="item.id"
        class="order-card"
      >
        <view class="order-header">
          <view class="order-title">{{ item.objectName || "--" }}</view>
          <view style="display: flex; gap: 8rpx">
            <view
              class="order-status"
              :class="'status-' + item.objectStatus"
            >{{ statusMap[item.objectStatus] }}</view>
            <view
              class="settle-status"
              :class="'settle-status-' + getSettleStatus(item)"
            >
              {{ getSettleStatusText(item) }}
            </view>
          </view>
        </view>
        
        <view class="order-detail-row">
          <view class="order-label">任务金额：</view>
          <view class="order-value price">¥{{ item.totalPrice || 0 }}</view>
        </view>
        <!-- 申请时间 -->
        <view class="order-detail-row">
          <view class="order-label">申请时间：</view>
          <view class="order-value">{{ item.createTime }}</view>
        </view>
        <!-- 申请备注 -->
        <view class="order-detail-row">
          <view class="order-label">申请备注：</view>
          <view class="order-value">{{ item.applyContent || "--" }}</view>
        </view>

        <!-- 操作区 -->
        <view class="order-actions" v-if="item.payStatus === 3">
          <u-button
            type="primary"
            plain
            class="action-btn primary"
            @tap.stop="handleApplySettle(item)"
          >重新申请</u-button>
        </view>
      </view>
      <uni-load-more
        :status="list.length == page.total ? 'noMore' : 'loading'"
      ></uni-load-more>
    </view>

    <!-- 任务详情弹窗 -->
    <u-popup
      :show="showDetailPopup"
      mode="bottom"
      @close="showDetailPopup = false"
      :closeable="true"
    >
      <view class="detail-popup">
        <view class="popup-title">任务详情</view>
        <view class="detail-content" v-if="currentItem">
          <view class="detail-item">
            <view class="detail-label">任务名称：</view>
            <view class="detail-value">{{ currentItem.objectName }}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">任务金额：</view>
            <view class="detail-value price">¥{{ currentItem.totalPrice }}</view>
          </view>
          <view class="detail-item" v-if="currentItem.totalPrice">
            <view class="detail-label">结算金额：</view>
            <view class="detail-value settle-amount">¥{{ currentItem.totalPrice }}</view>
          </view>
         
          <view class="detail-item" v-if="currentItem.settleTime">
            <view class="detail-label">结算时间：</view>
            <view class="detail-value">{{ currentItem.settleTime }}</view>
          </view>
          <view class="detail-item" v-if="currentItem.auditRemark">
            <view class="detail-label">{{ getSettleStatus(currentItem) == '3' ? '失败原因：' : '结算备注：' }}</view>
            <view class="detail-value" :class="{ 'reject-reason': getSettleStatus(currentItem) === '3' }">{{ currentItem.auditRemark }}</view>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 申请结算弹窗 -->
    <u-popup
      :show="showSettlePopup"
      mode="bottom"
      @close="showSettlePopup = false"
      :closeable="true"
    >
      <view class="settle-popup">
        <view class="popup-title">申请结算</view>
        <view class="popup-content">
          <u-form
            labelPosition="top"
            labelWidth="auto"
            :model="settleForm"
            ref="settleForm"
          >
            <u-form-item borderBottom labelPosition="left"  label="结算金额">
              <u-input
                v-model="settleForm.totalPrice"
                placeholder="请输入结算金额"
                border="none"
                type="number"
              />
            </u-form-item>
            <u-form-item borderBottom label="备注">
              <u-textarea
                v-model="settleForm.applyContent"
                border="none"
                placeholder="请输入备注信息"
              ></u-textarea>
            </u-form-item>
          </u-form>
        </view>
        <view class="popup-footer">
          <u-button type="primary" @click="submitSettle">提交申请</u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>
<script>
import { dateFormat } from "../../utils/date";

export default {
  name: "Settle",
  data() {
    return {
      dateFormat,
      keyWords: "",
      currentTab: 0,
      list: [],
      page: {
        size: 10,
        current: 1,
        total: 0,
      },
      // 结算金额统计
      summary: {
        totalAmount: 0,
        pendingAmount: 0,
        processingAmount: 0,
        settledAmount: 0,
      },
      // Tab列表
      tabList: [
        // {
        //   label: "待结算",
        //   value: "",
        // },
        {
          label: "待审核",
          value: "0",
        },
        {
          label: "审核通过",
          value: "1",
        },
        {
          label: "已付款",
          value: "2",
        },
        {
          label: "审核失败",
          value: "3",
        },
      ],
      // 状态映射
      statusMap: {
        2: "已完成",
        3: "待接单",
        1: "进行中",
      },
      // 筛选相关
      showFilterPopup: false,
      showStartDatePicker: false,
      showEndDatePicker: false,
      filterForm: {
        minAmount: "",
        maxAmount: "",
        startDate: "",
        endDate: "",
      },
      // 弹窗控制
      showDetailPopup: false,
      showSettlePopup: false,
      currentItem: null,
      // 申请结算表单
      settleForm: {
        id: "",
        totalPrice: "",
        applyContent: "",
      },
    };
  },
  onReady() {
    this.getList();
    this.getSettleSummary();
  },
  onShow() {
    // 可以在这里添加页面显示时的逻辑
  },
  onPullDownRefresh() {
    this.refreshData();
  },
  onReachBottom() {
    this.scrolltolower();
  },
  methods: {
    // 获取结算统计数据
    getSettleSummary() {
      this.$u.api.getSettleSummary().then((data) => {
        this.summary = data.data || {
          totalAmount: 0,
          pendingAmount: 0,
          processingAmount: 0,
          settledAmount: 0,
        };
      }).catch(() => {
        // 如果接口不存在，使用模拟数据
        this.summary = {
          totalAmount: 18680.50,
          pendingAmount: 3200.00,
          processingAmount: 6580.50, // 包含待审核、审核通过、审核失败
          settledAmount: 8900.00,
        };
      });
    },
    // 刷新数据
    refreshData() {
      this.page.current = 1;
      this.list = [];
      this.getList();
      this.getSettleSummary();
      uni.stopPullDownRefresh();
    },
    // 获取任务列表
    getList() {
      const params = {
        size: this.page.size,
        current: this.page.current,
        
        objectName: this.keyWords,
      };

      // 根据当前Tab设置支付状态筛选
      const currentTabValue = this.tabList[this.currentTab].value;
      params.payStatus = currentTabValue;

      // 添加筛选条件
      if (this.filterForm.minAmount) {
        params.minAmount = this.filterForm.minAmount;
      }
      if (this.filterForm.maxAmount) {
        params.maxAmount = this.filterForm.maxAmount;
      }
      if (this.filterForm.startDate) {
        params.startDate = this.filterForm.startDate;
      }
      if (this.filterForm.endDate) {
        params.endDate = this.filterForm.endDate;
      }

      this.$u.api
        .getSettlementApply(params)
        .then((res) => {
          this.list = [...this.list, ...res.data.records];
          this.page.total = res.data.total;
        })
        .catch(() => {
          // 如果接口不存在，使用模拟数据
          this.loadMockData();
        });
    },
    // 加载模拟数据
    loadMockData() {
      const currentTabValue = this.tabList[this.currentTab].value;
      let mockData = [];

      if (currentTabValue === 'pending') {
        // 待结算任务
        mockData = [
          {
            id: 1,
            objectName: "空调维修任务",
            serverTypeName: "维修服务",
            finalCustomer: "张三公司",
            contact: "张三",
            contactPhone: "13800138001",
            distributionAddress: "北京市朝阳区xxx街道",
            completeTime: "2024-01-15 14:30:00",
            totalPrice: 1200.00,
            objectStatus: 2,
            payStatus: null,
          },
        ];
      } else if (currentTabValue === 'auditing') {
        // 待审核任务
        mockData = [
          {
            id: 2,
            objectName: "电梯保养任务",
            serverTypeName: "保养服务",
            finalCustomer: "李四大厦",
            contact: "李四",
            contactPhone: "13800138002",
            distributionAddress: "上海市浦东新区xxx路",
            completeTime: "2024-01-14 16:45:00",
            totalPrice: 800.00,
            settleAmount: 800.00,
            objectStatus: 2,
            payStatus: 0,
          },
        ];
      } else if (currentTabValue === 'approved') {
        // 审核通过任务
        mockData = [
          {
            id: 3,
            objectName: "网络设备维护",
            serverTypeName: "维护服务",
            finalCustomer: "王五科技",
            contact: "王五",
            contactPhone: "13800138003",
            distributionAddress: "深圳市南山区xxx大厦",
            completeTime: "2024-01-13 10:20:00",
            totalPrice: 1500.00,
            settleAmount: 1500.00,
            objectStatus: 2,
            payStatus: 1,
          },
        ];
      } else if (currentTabValue === 'settled') {
        // 已付款任务
        mockData = [
          {
            id: 4,
            objectName: "消防设备检修",
            serverTypeName: "检修服务",
            finalCustomer: "赵六集团",
            contact: "赵六",
            contactPhone: "13800138004",
            distributionAddress: "广州市天河区xxx中心",
            completeTime: "2024-01-12 15:45:00",
            totalPrice: 2000.00,
            settleAmount: 2000.00,
            settleTime: "2024-01-13 09:30:00",
            objectStatus: 2,
            payStatus: 2,
          },
        ];
      } else if (currentTabValue === 'rejected') {
        // 审核失败任务
        mockData = [
          {
            id: 5,
            objectName: "监控系统维修",
            serverTypeName: "维修服务",
            finalCustomer: "孙七物业",
            contact: "孙七",
            contactPhone: "13800138005",
            distributionAddress: "杭州市西湖区xxx小区",
            completeTime: "2024-01-11 13:15:00",
            totalPrice: 900.00,
            settleAmount: 900.00,
            auditRemark: "审核失败：费用明细不清晰，请重新提交",
            objectStatus: 2,
            payStatus: 3,
          },
        ];
      }

      this.list = [...this.list, ...mockData];
      this.page.total = mockData.length;
    },
    // 滚动到底部加载更多
    scrolltolower() {
      if (this.list.length == this.page.total) return;
      this.page.current++;
      this.getList();
    },
    // Tab切换
    handleTabChange(index) {
      this.currentTab = index;
      this.page.current = 1;
      this.list = [];
      this.getList();
    },
    // 搜索
    handleSearch() {
      this.list = [];
      this.page.current = 1;
      this.getList();
    },
    // 筛选相关方法
    resetFilter() {
      this.filterForm = {
        minAmount: "",
        maxAmount: "",
        startDate: "",
        endDate: "",
      };
    },
    applyFilter() {
      this.showFilterPopup = false;
      this.page.current = 1;
      this.list = [];
      this.getList();
    },
    // 获取结算状态
    getSettleStatus(item) {
      if (item.payStatus === null || item.payStatus === undefined) {
        return "pending"; // 待结算
      }else{
        return item.payStatus
      }
     
    },
    // 获取结算状态文本
    getSettleStatusText(item) {
    //   const status = this.getSettleStatus(item);
      const statusMap = {
        0: "待审核",
        1: "审核通过",
        2: "已付款",
        3: "审核失败",
      };
      return statusMap[item.payStatus] ;
    },
    // 显示任务详情
    showOrderDetail(item) {
      this.currentItem = item;
      this.showDetailPopup = true;
    },
    // 申请结算
    handleApplySettle(item) {
      this.currentItem = item;
      this.settleForm = {
        id: item.id,
        totalPrice: item.totalPrice,
        applyContent: "",
        payStatus:0
      };
      this.showSettlePopup = true;
    },
    // 提交结算申请
    submitSettle() {
      if (!this.settleForm.totalPrice) {
        this.$u.toast("请输入结算金额");
        return;
      }

      this.$u.api.editSettlement(this.settleForm).then(() => {
        this.$u.toast("申请成功");
        this.showSettlePopup = false;
        this.settleForm = {
          id: "",
          totalPrice: "",
          applyContent: "",
        };
        this.refreshData();
      }).catch((err) => {
        this.$u.toast(err.message || "申请失败");
      });
    },
    // 拨打电话
    makePhoneCall(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
        success() {
          console.log("拨打电话成功！");
        },
        fail(err) {
          console.log("拨打电话失败！", err);
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.workbench-wrap {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 32rpx 0 0 0;
  box-sizing: border-box;
}

// 结算金额统计卡片
.summary-card {
  margin: 32rpx 32rpx 24rpx 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.06);
  padding: 32rpx 24rpx 24rpx 24rpx;
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #333;
}

.summary-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-label {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d8cf0;
}

.stat-item.total .stat-value {
  color: #2d8cf0;
}

.stat-item.pending .stat-value {
  color: #ff9900;
}

.stat-item.processing .stat-value {
  color: #19be6b;
}

.stat-item.finished .stat-value {
  color: #909399;
}

// 搜索容器
.search-container {
  display: flex;
  align-items: center;
  margin: 0 32rpx 16rpx 32rpx;
  gap: 16rpx;
}

.search-box {
  flex: 1;
}

.filter-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 68rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);

  text {
    font-size: 20rpx;
    color: #666;
    margin-top: 4rpx;
  }
}

// Tab栏
.order-tab-bar {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin: 0 32rpx 16rpx 32rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #888;
  background: #fff;
  transition: all 0.2s;
}

.tab-item.active {
  color: #2d8cf0;
  font-weight: bold;
  background: #e6f7ff;
}

// 任务列表
.order-list {
  margin: 0 32rpx;
}

.order-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.04);
  margin-bottom: 24rpx;
  padding: 24rpx 20rpx 16rpx 20rpx;
  transition: box-shadow 0.2s;
}

.order-card:hover {
  box-shadow: 0 8rpx 32rpx 0 rgba(45, 140, 240, 0.08);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.order-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.order-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
  background: #f5f7fa;
}

.order-status.status-2 {
  color: #19be6b;
  background: #e6ffed;
}

.settle-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.settle-status.settle-status- {
  color: #ff9900;
  background: #fff7e6;
}

.settle-status.settle-status-0 {
  color: #2d8cf0;
  background: #e6f7ff;
}

.settle-status.settle-status-1 {
  color: #19be6b;
  background: #e6ffed;
}

.settle-status.settle-status-2 {
  color: #67c23a;
  background: #f0f9ff;
}

.settle-status.settle-status-3 {
  color: #f56c6c;
  background: #fef0f0;
}

.order-detail-row {
  display: flex;
  align-items: flex-start;
  font-size: 26rpx;
  color: #555;
  margin-bottom: 8rpx;
}

.order-label {
  min-width: 120rpx;
  color: #888;
  font-weight: 400;
}

.order-value {
  flex: 1;
  color: #333;
  word-break: break-all;
}

.order-value.price {
  color: #f56c6c;
  font-weight: 600;
}

.order-value.settle-amount {
  color: #19be6b;
  font-weight: 600;
}

.order-actions {
  display: flex;
  gap: 24rpx;
  border-top: 1px solid #f0f0f0;
  margin-top: 18rpx;
  padding-top: 18rpx;
  justify-content: flex-end;
  background: #fff;
}

.action-btn {
  min-width: 120rpx;
  padding: 0 32rpx;
  height: 56rpx;
  line-height: 56rpx;
  border: none;
  border-radius: 32rpx;
  background: #f5f7fa;
  color: #2d8cf0;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0;
  outline: none;
  box-shadow: 0 2rpx 8rpx 0 rgba(45, 140, 240, 0.04);
  transition: background 0.2s, color 0.2s;
}

.action-btn.primary {
  background: linear-gradient(90deg, #2d8cf0 0%, #57a3f3 100%);
  color: #fff;
}

.action-btn:active {
  opacity: 0.85;
}

.empty-tip {
  text-align: center;
  color: #bbb;
  font-size: 28rpx;
  margin: 64rpx 0;
}

// 筛选弹窗
.filter-popup {
  padding: 30rpx;

  .popup-title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
  }
}

.filter-content {
  margin-bottom: 30rpx;
}

.filter-item {
  margin-bottom: 30rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.amount-range,
.date-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.filter-footer {
  display: flex;
  gap: 24rpx;

  .u-button {
    flex: 1;
  }
}

// 详情弹窗
.detail-popup {
  padding: 30rpx;
  max-height: 70vh;
  overflow-y: auto;

  .popup-title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
  }
}

.detail-content {
  margin-bottom: 30rpx;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  font-size: 28rpx;
}

.detail-label {
  min-width: 140rpx;
  color: #888;
  font-weight: 400;
}

.detail-value {
  flex: 1;
  color: #333;
  word-break: break-all;
}

.detail-value.price {
  color: #f56c6c;
  font-weight: 600;
}

.detail-value.settle-amount {
  color: #19be6b;
  font-weight: 600;
}

.detail-value.reject-reason {
  color: #f56c6c;
  font-weight: 500;
  background: #fef0f0;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #f56c6c;
}

// 申请结算弹窗
.settle-popup {
  padding: 30rpx;

  .popup-title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
  }

  .popup-content {
    margin-bottom: 30rpx;
  }

  .popup-footer {
    padding: 20rpx 0;
  }
}

::v-deep input {
  text-align: right !important;
}
</style>