@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
.workbench-wrap.data-v-362ca366 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 32rpx 0 0 0;
  box-sizing: border-box;
}
.summary-card.data-v-362ca366 {
  margin: 32rpx 32rpx 24rpx 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.06);
  padding: 32rpx 24rpx 24rpx 24rpx;
}
.summary-title.data-v-362ca366 {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #333;
}
.summary-stats.data-v-362ca366 {
  display: flex;
  justify-content: space-between;
}
.stat-item.data-v-362ca366 {
  flex: 1;
  text-align: center;
}
.stat-label.data-v-362ca366 {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 8rpx;
}
.stat-value.data-v-362ca366 {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d8cf0;
}
.stat-item.total .stat-value.data-v-362ca366 {
  color: #2d8cf0;
}
.stat-item.pending .stat-value.data-v-362ca366 {
  color: #ff9900;
}
.stat-item.processing .stat-value.data-v-362ca366 {
  color: #19be6b;
}
.stat-item.finished .stat-value.data-v-362ca366 {
  color: #909399;
}
.search-container.data-v-362ca366 {
  display: flex;
  align-items: center;
  margin: 0 32rpx 16rpx 32rpx;
  gap: 16rpx;
}
.search-box.data-v-362ca366 {
  flex: 1;
}
.filter-btn.data-v-362ca366 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 68rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);
}
.filter-btn text.data-v-362ca366 {
  font-size: 20rpx;
  color: #666;
  margin-top: 4rpx;
}
.order-tab-bar.data-v-362ca366 {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin: 0 32rpx 16rpx 32rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);
  overflow: hidden;
}
.tab-item.data-v-362ca366 {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #888;
  background: #fff;
  transition: all 0.2s;
}
.tab-item.active.data-v-362ca366 {
  color: #2d8cf0;
  font-weight: bold;
  background: #e6f7ff;
}
.order-list.data-v-362ca366 {
  margin: 0 32rpx;
}
.order-card.data-v-362ca366 {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.04);
  margin-bottom: 24rpx;
  padding: 24rpx 20rpx 16rpx 20rpx;
  transition: box-shadow 0.2s;
}
.order-card.data-v-362ca366:hover {
  box-shadow: 0 8rpx 32rpx 0 rgba(45, 140, 240, 0.08);
}
.order-header.data-v-362ca366 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.order-title.data-v-362ca366 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.order-status.data-v-362ca366 {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
  background: #f5f7fa;
}
.order-status.status-2.data-v-362ca366 {
  color: #19be6b;
  background: #e6ffed;
}
.settle-status.data-v-362ca366 {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}
.settle-status.settle-status-.data-v-362ca366 {
  color: #ff9900;
  background: #fff7e6;
}
.settle-status.settle-status-0.data-v-362ca366 {
  color: #2d8cf0;
  background: #e6f7ff;
}
.settle-status.settle-status-1.data-v-362ca366 {
  color: #19be6b;
  background: #e6ffed;
}
.settle-status.settle-status-2.data-v-362ca366 {
  color: #67c23a;
  background: #f0f9ff;
}
.settle-status.settle-status-3.data-v-362ca366 {
  color: #f56c6c;
  background: #fef0f0;
}
.order-detail-row.data-v-362ca366 {
  display: flex;
  align-items: flex-start;
  font-size: 26rpx;
  color: #555;
  margin-bottom: 8rpx;
}
.order-label.data-v-362ca366 {
  min-width: 120rpx;
  color: #888;
  font-weight: 400;
}
.order-value.data-v-362ca366 {
  flex: 1;
  color: #333;
  word-break: break-all;
}
.order-value.price.data-v-362ca366 {
  color: #f56c6c;
  font-weight: 600;
}
.order-value.settle-amount.data-v-362ca366 {
  color: #19be6b;
  font-weight: 600;
}
.order-actions.data-v-362ca366 {
  display: flex;
  gap: 24rpx;
  border-top: 1px solid #f0f0f0;
  margin-top: 18rpx;
  padding-top: 18rpx;
  justify-content: flex-end;
  background: #fff;
}
.action-btn.data-v-362ca366 {
  min-width: 120rpx;
  padding: 0 32rpx;
  height: 56rpx;
  line-height: 56rpx;
  border: none;
  border-radius: 32rpx;
  background: #f5f7fa;
  color: #2d8cf0;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0;
  outline: none;
  box-shadow: 0 2rpx 8rpx 0 rgba(45, 140, 240, 0.04);
  transition: background 0.2s, color 0.2s;
}
.action-btn.primary.data-v-362ca366 {
  background: linear-gradient(90deg, #2d8cf0 0%, #57a3f3 100%);
  color: #fff;
}
.action-btn.data-v-362ca366:active {
  opacity: 0.85;
}
.empty-tip.data-v-362ca366 {
  text-align: center;
  color: #bbb;
  font-size: 28rpx;
  margin: 64rpx 0;
}
.filter-popup.data-v-362ca366 {
  padding: 30rpx;
}
.filter-popup .popup-title.data-v-362ca366 {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}
.filter-content.data-v-362ca366 {
  margin-bottom: 30rpx;
}
.filter-item.data-v-362ca366 {
  margin-bottom: 30rpx;
}
.filter-label.data-v-362ca366 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}
.amount-range.data-v-362ca366,
.date-range.data-v-362ca366 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.filter-footer.data-v-362ca366 {
  display: flex;
  gap: 24rpx;
}
.filter-footer .u-button.data-v-362ca366 {
  flex: 1;
}
.detail-popup.data-v-362ca366 {
  padding: 30rpx;
  max-height: 70vh;
  overflow-y: auto;
}
.detail-popup .popup-title.data-v-362ca366 {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}
.detail-content.data-v-362ca366 {
  margin-bottom: 30rpx;
}
.detail-item.data-v-362ca366 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  font-size: 28rpx;
}
.detail-label.data-v-362ca366 {
  min-width: 140rpx;
  color: #888;
  font-weight: 400;
}
.detail-value.data-v-362ca366 {
  flex: 1;
  color: #333;
  word-break: break-all;
}
.detail-value.price.data-v-362ca366 {
  color: #f56c6c;
  font-weight: 600;
}
.detail-value.settle-amount.data-v-362ca366 {
  color: #19be6b;
  font-weight: 600;
}
.detail-value.reject-reason.data-v-362ca366 {
  color: #f56c6c;
  font-weight: 500;
  background: #fef0f0;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #f56c6c;
}
.settle-popup.data-v-362ca366 {
  padding: 30rpx;
}
.settle-popup .popup-title.data-v-362ca366 {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}
.settle-popup .popup-content.data-v-362ca366 {
  margin-bottom: 30rpx;
}
.settle-popup .popup-footer.data-v-362ca366 {
  padding: 20rpx 0;
}
.data-v-362ca366 input {
  text-align: right !important;
}

