<view class="user"><view class="bg"></view><view class="user-info-box"><u-avatar vue-id="e8437cc0-1" src="{{userInfo.avatar?userInfo.avatar:user_avg}}" shape="square" bind:__l="__l"></u-avatar><view data-event-opts="{{[['tap',[['toLogin']]]]}}" class="user-info-right" bindtap="__e"><view class="user-nickname">{{userInfo.name?userInfo.name:user_name}}</view><view class="user-phone">{{''+(userInfo.phone?$root.g0:'')+''}}</view></view></view><view class="user-activity-menu"><view data-event-opts="{{[['tap',[['toUserInfo',['$event']]]]]}}" class="menu-item" bindtap="__e"><view class="left"><image class="menu-icon" src="../../static/icon/icon_userinfo.png"></image><view class="menu-name">个人资料</view></view><u-icon vue-id="e8437cc0-2" name="arrow-right" bind:__l="__l"></u-icon></view><view data-event-opts="{{[['tap',[['toMyWokerOrder',['$event']]]]]}}" class="menu-item" bindtap="__e"><view class="left"><image class="menu-icon" src="../../static/img/pay.png"></image><view class="menu-name">任务中心</view></view><u-icon vue-id="e8437cc0-3" name="arrow-right" bind:__l="__l"></u-icon></view><view data-event-opts="{{[['tap',[['toSettle',['$event']]]]]}}" class="menu-item" bindtap="__e"><view class="left"><image class="menu-icon" src="../../static/img/pay.png"></image><view class="menu-name">结算中心</view></view><u-icon vue-id="e8437cc0-4" name="arrow-right" bind:__l="__l"></u-icon></view></view></view>