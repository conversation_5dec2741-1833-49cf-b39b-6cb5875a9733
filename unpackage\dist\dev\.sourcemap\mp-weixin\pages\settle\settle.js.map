{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/project/vt-unih5-order/pages/settle/settle.vue?5e4c", "webpack:///D:/project/vt-unih5-order/pages/settle/settle.vue?e9e3", "webpack:///D:/project/vt-unih5-order/pages/settle/settle.vue?4259", "uni-app:///pages/settle/settle.vue", "webpack:///D:/project/vt-unih5-order/pages/settle/settle.vue?064e", "webpack:///D:/project/vt-unih5-order/pages/settle/settle.vue?6815"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "dateFormat", "key<PERSON>ords", "currentTab", "list", "page", "size", "current", "total", "summary", "totalAmount", "pendingAmount", "processingAmount", "settledAmount", "tabList", "label", "value", "statusMap", "showFilterPopup", "showStartDatePicker", "showEndDatePicker", "filterForm", "minAmount", "maxAmount", "startDate", "endDate", "showDetailPopup", "showSettlePopup", "currentItem", "settleForm", "id", "totalPrice", "applyContent", "onReady", "onShow", "onPullDownRefresh", "onReachBottom", "methods", "getSettleSummary", "refreshData", "uni", "getList", "objectName", "params", "getSettlementApply", "then", "catch", "loadMockData", "mockData", "serverTypeName", "finalCustomer", "contact", "contactPhone", "distributionAddress", "completeTime", "objectStatus", "payStatus", "settleAmount", "settleTime", "auditRemark", "scrolltolower", "handleTabChange", "handleSearch", "resetFilter", "applyFilter", "getSettleStatus", "getSettleStatusText", "showOrderDetail", "handleApplySettle", "submitSettle", "makePhoneCall", "phoneNumber", "success", "console", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzIA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACyQvnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;MACA;MACA;MACA;MACA;MACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACA;MACAC;QACA;QACA;QACA;MACA;MACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;MACAC;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EAAA,CACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACA;UACA5B;UACAC;UACAC;UACAC;QACA;MACA;QACA;QACA;UACAH;UACAC;UACAC;UAAA;UACAC;QACA;MACA;IACA;IACA;IACA0B;MACA;MACA;MACA;MACA;MACAC;IACA;IACA;IACAC;MAAA;MACA;QACAnC;QACAC;QAEAmC;MACA;;MAEA;MACA;MACAC;;MAEA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MAEA,YACAC,2BACAC;QACA;QACA;MACA,GACAC;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MAEA;QACA;QACAC,YACA;UACAlB;UACAY;UACAO;UACAC;UACAC;UACAC;UACAC;UACAC;UACAvB;UACAwB;UACAC;QACA,EACA;MACA;QACA;QACAR,YACA;UACAlB;UACAY;UACAO;UACAC;UACAC;UACAC;UACAC;UACAC;UACAvB;UACA0B;UACAF;UACAC;QACA,EACA;MACA;QACA;QACAR,YACA;UACAlB;UACAY;UACAO;UACAC;UACAC;UACAC;UACAC;UACAC;UACAvB;UACA0B;UACAF;UACAC;QACA,EACA;MACA;QACA;QACAR,YACA;UACAlB;UACAY;UACAO;UACAC;UACAC;UACAC;UACAC;UACAC;UACAvB;UACA0B;UACAC;UACAH;UACAC;QACA,EACA;MACA;QACA;QACAR,YACA;UACAlB;UACAY;UACAO;UACAC;UACAC;UACAC;UACAC;UACAC;UACAvB;UACA0B;UACAE;UACAJ;UACAC;QACA,EACA;MACA;MAEA;MACA;IACA;IACA;IACAI;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACAzC;QACAC;QACAC;QACAC;MACA;IACA;IACAuC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IAEA;IACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAtC;QACAC;QACAC;QACAwB;MACA;MACA;IACA;IACA;IACAa;MAAA;MACA;QACA;QACA;MACA;MAEA;QACA;QACA;QACA;UACAvC;UACAC;UACAC;QACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAsC;MACA9B;QACA+B;QACAC;UACAC;QACA;QACAC;UACAD;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7mBA;AAAA;AAAA;AAAA;AAAsqC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACA1rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/settle/settle.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/settle/settle.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./settle.vue?vue&type=template&id=362ca366&scoped=true&\"\nvar renderjs\nimport script from \"./settle.vue?vue&type=script&lang=js&\"\nexport * from \"./settle.vue?vue&type=script&lang=js&\"\nimport style0 from \"./settle.vue?vue&type=style&index=0&id=362ca366&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"362ca366\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/settle/settle.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settle.vue?vue&type=template&id=362ca366&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-search/u-search\" */ \"@/uni_modules/uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker\" */ \"@/uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uTextarea: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-textarea/u-textarea\" */ \"@/uni_modules/uview-ui/components/u-textarea/u-textarea.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var l0 = _vm.__map(_vm.list, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.getSettleStatus(item)\n    var m1 = _vm.getSettleStatusText(item)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  var g1 = _vm.list.length\n  var m2 =\n    _vm.currentItem && _vm.currentItem.auditRemark\n      ? _vm.getSettleStatus(_vm.currentItem)\n      : null\n  var m3 =\n    _vm.currentItem && _vm.currentItem.auditRemark\n      ? _vm.getSettleStatus(_vm.currentItem)\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showDetailPopup = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showFilterPopup = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showStartDatePicker = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.showEndDatePicker = true\n    }\n    _vm.e4 = function ($event) {\n      _vm.showStartDatePicker = false\n    }\n    _vm.e5 = function ($event) {\n      _vm.showStartDatePicker = false\n    }\n    _vm.e6 = function ($event) {\n      _vm.showEndDatePicker = false\n    }\n    _vm.e7 = function ($event) {\n      _vm.showEndDatePicker = false\n    }\n    _vm.e8 = function ($event) {\n      _vm.showDetailPopup = false\n    }\n    _vm.e9 = function ($event) {\n      _vm.showSettlePopup = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settle.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settle.vue?vue&type=script&lang=js&\"", "<template>\r\n  <page-meta\r\n    :page-style=\"\r\n      'overflow:' + (showDetailPopup ? 'hidden' : 'visible')\r\n    \"\r\n  ></page-meta>\r\n  <page-container\r\n    :show=\"showDetailPopup\"\r\n    @beforeleave=\"showDetailPopup = false\"\r\n  ></page-container>\r\n  <view class=\"workbench-wrap\">\r\n    <!-- 结算金额统计卡片 -->\r\n    <view class=\"summary-card\">\r\n      <view class=\"summary-title\">结算概况</view>\r\n      <view class=\"summary-stats\">\r\n        <!-- <view class=\"stat-item total\">\r\n          <view class=\"stat-label\">全部金额</view>\r\n          <view class=\"stat-value\">¥{{ summary.totalAmount }}</view>\r\n        </view> -->\r\n        <view class=\"stat-item pending\">\r\n          <view class=\"stat-label\">待结算</view>\r\n          <view class=\"stat-value\">¥{{ summary.pendingAmount }}</view>\r\n        </view>\r\n        <view class=\"stat-item processing\">\r\n          <view class=\"stat-label\">结算中</view>\r\n          <view class=\"stat-value\">¥{{ summary.processingAmount }}</view>\r\n        </view>\r\n        <view class=\"stat-item finished\">\r\n          <view class=\"stat-label\">已结算</view>\r\n          <view class=\"stat-value\">¥{{ summary.settledAmount }}</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 搜索框 -->\r\n    <view class=\"search-container\">\r\n      <view class=\"search-box\">\r\n        <u-search\r\n          shape=\"square\"\r\n          v-model=\"keyWords\"\r\n          placeholder=\"搜索任务名称、客户名称、联系人\"\r\n          :showAction=\"true\"\r\n          actionText=\"搜索\"\r\n          @search=\"handleSearch\"\r\n          @custom=\"handleSearch\"\r\n        ></u-search>\r\n      </view>\r\n      <!-- 筛选按钮 -->\r\n      <!-- <view class=\"filter-btn\" @click=\"showFilterPopup = true\">\r\n        <u-icon name=\"list\" size=\"20\" color=\"#666\"></u-icon>\r\n        <text>筛选</text>\r\n      </view> -->\r\n    </view>\r\n\r\n    <!-- 结算状态tab栏 -->\r\n    <view class=\"order-tab-bar\">\r\n      <view\r\n        v-for=\"(tab, idx) in tabList\"\r\n        :key=\"tab.value\"\r\n        :class=\"['tab-item', { active: currentTab === idx }]\"\r\n        @tap=\"handleTabChange(idx)\"\r\n      >\r\n        {{ tab.label }}\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 筛选弹窗 -->\r\n    <u-popup\r\n      :show=\"showFilterPopup\"\r\n      mode=\"bottom\"\r\n      @close=\"showFilterPopup = false\"\r\n      :closeable=\"true\"\r\n    >\r\n      <view class=\"filter-popup\">\r\n        <view class=\"popup-title\">筛选条件</view>\r\n        <view class=\"filter-content\">\r\n          <view class=\"filter-item\">\r\n            <view class=\"filter-label\">金额范围</view>\r\n            <view class=\"amount-range\">\r\n              <u-input\r\n                v-model=\"filterForm.minAmount\"\r\n                placeholder=\"最小金额\"\r\n                type=\"number\"\r\n              />\r\n              <text style=\"margin: 0 10rpx;\">至</text>\r\n              <u-input\r\n                v-model=\"filterForm.maxAmount\"\r\n                placeholder=\"最大金额\"\r\n                type=\"number\"\r\n              />\r\n            </view>\r\n          </view>\r\n          <view class=\"filter-item\">\r\n            <view class=\"filter-label\">结算时间</view>\r\n            <view class=\"date-range\">\r\n              <u-input\r\n                v-model=\"filterForm.startDate\"\r\n                placeholder=\"开始日期\"\r\n                type=\"select\"\r\n                @click=\"showStartDatePicker = true\"\r\n              />\r\n              <text style=\"margin: 0 10rpx;\">至</text>\r\n              <u-input\r\n                v-model=\"filterForm.endDate\"\r\n                placeholder=\"结束日期\"\r\n                type=\"select\"\r\n                @click=\"showEndDatePicker = true\"\r\n              />\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"filter-footer\">\r\n          <u-button type=\"info\" plain @click=\"resetFilter\">重置</u-button>\r\n          <u-button type=\"primary\" @click=\"applyFilter\">确定</u-button>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n\r\n    <!-- 日期选择器 -->\r\n    <u-datetime-picker\r\n      v-model=\"filterForm.startDate\"\r\n      :show=\"showStartDatePicker\"\r\n      @cancel=\"showStartDatePicker = false\"\r\n      @confirm=\"showStartDatePicker = false\"\r\n      mode=\"date\"\r\n    ></u-datetime-picker>\r\n    <u-datetime-picker\r\n      v-model=\"filterForm.endDate\"\r\n      :show=\"showEndDatePicker\"\r\n      @cancel=\"showEndDatePicker = false\"\r\n      @confirm=\"showEndDatePicker = false\"\r\n      mode=\"date\"\r\n    ></u-datetime-picker>\r\n\r\n    <!-- 任务列表 -->\r\n    <view class=\"order-list\">\r\n      <view v-if=\"list.length === 0\" class=\"empty-tip\"> 暂无结算任务 </view>\r\n      <view\r\n        v-for=\"item in list\"\r\n        @click.stop=\"showOrderDetail(item)\"\r\n        :key=\"item.id\"\r\n        class=\"order-card\"\r\n      >\r\n        <view class=\"order-header\">\r\n          <view class=\"order-title\">{{ item.objectName || \"--\" }}</view>\r\n          <view style=\"display: flex; gap: 8rpx\">\r\n            <view\r\n              class=\"order-status\"\r\n              :class=\"'status-' + item.objectStatus\"\r\n            >{{ statusMap[item.objectStatus] }}</view>\r\n            <view\r\n              class=\"settle-status\"\r\n              :class=\"'settle-status-' + getSettleStatus(item)\"\r\n            >\r\n              {{ getSettleStatusText(item) }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">任务金额：</view>\r\n          <view class=\"order-value price\">¥{{ item.totalPrice || 0 }}</view>\r\n        </view>\r\n        <!-- 申请时间 -->\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">申请时间：</view>\r\n          <view class=\"order-value\">{{ item.createTime }}</view>\r\n        </view>\r\n        <!-- 申请备注 -->\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">申请备注：</view>\r\n          <view class=\"order-value\">{{ item.applyContent || \"--\" }}</view>\r\n        </view>\r\n\r\n        <!-- 操作区 -->\r\n        <view class=\"order-actions\" v-if=\"item.payStatus === 3\">\r\n          <u-button\r\n            type=\"primary\"\r\n            plain\r\n            class=\"action-btn primary\"\r\n            @tap.stop=\"handleApplySettle(item)\"\r\n          >重新申请</u-button>\r\n        </view>\r\n      </view>\r\n      <uni-load-more\r\n        :status=\"list.length == page.total ? 'noMore' : 'loading'\"\r\n      ></uni-load-more>\r\n    </view>\r\n\r\n    <!-- 任务详情弹窗 -->\r\n    <u-popup\r\n      :show=\"showDetailPopup\"\r\n      mode=\"bottom\"\r\n      @close=\"showDetailPopup = false\"\r\n      :closeable=\"true\"\r\n    >\r\n      <view class=\"detail-popup\">\r\n        <view class=\"popup-title\">任务详情</view>\r\n        <view class=\"detail-content\" v-if=\"currentItem\">\r\n          <view class=\"detail-item\">\r\n            <view class=\"detail-label\">任务名称：</view>\r\n            <view class=\"detail-value\">{{ currentItem.objectName }}</view>\r\n          </view>\r\n          <view class=\"detail-item\">\r\n            <view class=\"detail-label\">任务金额：</view>\r\n            <view class=\"detail-value price\">¥{{ currentItem.totalPrice }}</view>\r\n          </view>\r\n          <view class=\"detail-item\" v-if=\"currentItem.totalPrice\">\r\n            <view class=\"detail-label\">结算金额：</view>\r\n            <view class=\"detail-value settle-amount\">¥{{ currentItem.totalPrice }}</view>\r\n          </view>\r\n         \r\n          <view class=\"detail-item\" v-if=\"currentItem.settleTime\">\r\n            <view class=\"detail-label\">结算时间：</view>\r\n            <view class=\"detail-value\">{{ currentItem.settleTime }}</view>\r\n          </view>\r\n          <view class=\"detail-item\" v-if=\"currentItem.auditRemark\">\r\n            <view class=\"detail-label\">{{ getSettleStatus(currentItem) == '3' ? '失败原因：' : '结算备注：' }}</view>\r\n            <view class=\"detail-value\" :class=\"{ 'reject-reason': getSettleStatus(currentItem) === '3' }\">{{ currentItem.auditRemark }}</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n\r\n    <!-- 申请结算弹窗 -->\r\n    <u-popup\r\n      :show=\"showSettlePopup\"\r\n      mode=\"bottom\"\r\n      @close=\"showSettlePopup = false\"\r\n      :closeable=\"true\"\r\n    >\r\n      <view class=\"settle-popup\">\r\n        <view class=\"popup-title\">申请结算</view>\r\n        <view class=\"popup-content\">\r\n          <u-form\r\n            labelPosition=\"top\"\r\n            labelWidth=\"auto\"\r\n            :model=\"settleForm\"\r\n            ref=\"settleForm\"\r\n          >\r\n            <u-form-item borderBottom labelPosition=\"left\"  label=\"结算金额\">\r\n              <u-input\r\n                v-model=\"settleForm.totalPrice\"\r\n                placeholder=\"请输入结算金额\"\r\n                border=\"none\"\r\n                type=\"number\"\r\n              />\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"备注\">\r\n              <u-textarea\r\n                v-model=\"settleForm.applyContent\"\r\n                border=\"none\"\r\n                placeholder=\"请输入备注信息\"\r\n              ></u-textarea>\r\n            </u-form-item>\r\n          </u-form>\r\n        </view>\r\n        <view class=\"popup-footer\">\r\n          <u-button type=\"primary\" @click=\"submitSettle\">提交申请</u-button>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n  </view>\r\n</template>\r\n<script>\r\nimport { dateFormat } from \"../../utils/date\";\r\n\r\nexport default {\r\n  name: \"Settle\",\r\n  data() {\r\n    return {\r\n      dateFormat,\r\n      keyWords: \"\",\r\n      currentTab: 0,\r\n      list: [],\r\n      page: {\r\n        size: 10,\r\n        current: 1,\r\n        total: 0,\r\n      },\r\n      // 结算金额统计\r\n      summary: {\r\n        totalAmount: 0,\r\n        pendingAmount: 0,\r\n        processingAmount: 0,\r\n        settledAmount: 0,\r\n      },\r\n      // Tab列表\r\n      tabList: [\r\n        // {\r\n        //   label: \"待结算\",\r\n        //   value: \"\",\r\n        // },\r\n        {\r\n          label: \"待审核\",\r\n          value: \"0\",\r\n        },\r\n        {\r\n          label: \"审核通过\",\r\n          value: \"1\",\r\n        },\r\n        {\r\n          label: \"已付款\",\r\n          value: \"2\",\r\n        },\r\n        {\r\n          label: \"审核失败\",\r\n          value: \"3\",\r\n        },\r\n      ],\r\n      // 状态映射\r\n      statusMap: {\r\n        2: \"已完成\",\r\n        3: \"待接单\",\r\n        1: \"进行中\",\r\n      },\r\n      // 筛选相关\r\n      showFilterPopup: false,\r\n      showStartDatePicker: false,\r\n      showEndDatePicker: false,\r\n      filterForm: {\r\n        minAmount: \"\",\r\n        maxAmount: \"\",\r\n        startDate: \"\",\r\n        endDate: \"\",\r\n      },\r\n      // 弹窗控制\r\n      showDetailPopup: false,\r\n      showSettlePopup: false,\r\n      currentItem: null,\r\n      // 申请结算表单\r\n      settleForm: {\r\n        id: \"\",\r\n        totalPrice: \"\",\r\n        applyContent: \"\",\r\n      },\r\n    };\r\n  },\r\n  onReady() {\r\n    this.getList();\r\n    this.getSettleSummary();\r\n  },\r\n  onShow() {\r\n    // 可以在这里添加页面显示时的逻辑\r\n  },\r\n  onPullDownRefresh() {\r\n    this.refreshData();\r\n  },\r\n  onReachBottom() {\r\n    this.scrolltolower();\r\n  },\r\n  methods: {\r\n    // 获取结算统计数据\r\n    getSettleSummary() {\r\n      this.$u.api.getSettleSummary().then((data) => {\r\n        this.summary = data.data || {\r\n          totalAmount: 0,\r\n          pendingAmount: 0,\r\n          processingAmount: 0,\r\n          settledAmount: 0,\r\n        };\r\n      }).catch(() => {\r\n        // 如果接口不存在，使用模拟数据\r\n        this.summary = {\r\n          totalAmount: 18680.50,\r\n          pendingAmount: 3200.00,\r\n          processingAmount: 6580.50, // 包含待审核、审核通过、审核失败\r\n          settledAmount: 8900.00,\r\n        };\r\n      });\r\n    },\r\n    // 刷新数据\r\n    refreshData() {\r\n      this.page.current = 1;\r\n      this.list = [];\r\n      this.getList();\r\n      this.getSettleSummary();\r\n      uni.stopPullDownRefresh();\r\n    },\r\n    // 获取任务列表\r\n    getList() {\r\n      const params = {\r\n        size: this.page.size,\r\n        current: this.page.current,\r\n        \r\n        objectName: this.keyWords,\r\n      };\r\n\r\n      // 根据当前Tab设置支付状态筛选\r\n      const currentTabValue = this.tabList[this.currentTab].value;\r\n      params.payStatus = currentTabValue;\r\n\r\n      // 添加筛选条件\r\n      if (this.filterForm.minAmount) {\r\n        params.minAmount = this.filterForm.minAmount;\r\n      }\r\n      if (this.filterForm.maxAmount) {\r\n        params.maxAmount = this.filterForm.maxAmount;\r\n      }\r\n      if (this.filterForm.startDate) {\r\n        params.startDate = this.filterForm.startDate;\r\n      }\r\n      if (this.filterForm.endDate) {\r\n        params.endDate = this.filterForm.endDate;\r\n      }\r\n\r\n      this.$u.api\r\n        .getSettlementApply(params)\r\n        .then((res) => {\r\n          this.list = [...this.list, ...res.data.records];\r\n          this.page.total = res.data.total;\r\n        })\r\n        .catch(() => {\r\n          // 如果接口不存在，使用模拟数据\r\n          this.loadMockData();\r\n        });\r\n    },\r\n    // 加载模拟数据\r\n    loadMockData() {\r\n      const currentTabValue = this.tabList[this.currentTab].value;\r\n      let mockData = [];\r\n\r\n      if (currentTabValue === 'pending') {\r\n        // 待结算任务\r\n        mockData = [\r\n          {\r\n            id: 1,\r\n            objectName: \"空调维修任务\",\r\n            serverTypeName: \"维修服务\",\r\n            finalCustomer: \"张三公司\",\r\n            contact: \"张三\",\r\n            contactPhone: \"13800138001\",\r\n            distributionAddress: \"北京市朝阳区xxx街道\",\r\n            completeTime: \"2024-01-15 14:30:00\",\r\n            totalPrice: 1200.00,\r\n            objectStatus: 2,\r\n            payStatus: null,\r\n          },\r\n        ];\r\n      } else if (currentTabValue === 'auditing') {\r\n        // 待审核任务\r\n        mockData = [\r\n          {\r\n            id: 2,\r\n            objectName: \"电梯保养任务\",\r\n            serverTypeName: \"保养服务\",\r\n            finalCustomer: \"李四大厦\",\r\n            contact: \"李四\",\r\n            contactPhone: \"13800138002\",\r\n            distributionAddress: \"上海市浦东新区xxx路\",\r\n            completeTime: \"2024-01-14 16:45:00\",\r\n            totalPrice: 800.00,\r\n            settleAmount: 800.00,\r\n            objectStatus: 2,\r\n            payStatus: 0,\r\n          },\r\n        ];\r\n      } else if (currentTabValue === 'approved') {\r\n        // 审核通过任务\r\n        mockData = [\r\n          {\r\n            id: 3,\r\n            objectName: \"网络设备维护\",\r\n            serverTypeName: \"维护服务\",\r\n            finalCustomer: \"王五科技\",\r\n            contact: \"王五\",\r\n            contactPhone: \"13800138003\",\r\n            distributionAddress: \"深圳市南山区xxx大厦\",\r\n            completeTime: \"2024-01-13 10:20:00\",\r\n            totalPrice: 1500.00,\r\n            settleAmount: 1500.00,\r\n            objectStatus: 2,\r\n            payStatus: 1,\r\n          },\r\n        ];\r\n      } else if (currentTabValue === 'settled') {\r\n        // 已付款任务\r\n        mockData = [\r\n          {\r\n            id: 4,\r\n            objectName: \"消防设备检修\",\r\n            serverTypeName: \"检修服务\",\r\n            finalCustomer: \"赵六集团\",\r\n            contact: \"赵六\",\r\n            contactPhone: \"13800138004\",\r\n            distributionAddress: \"广州市天河区xxx中心\",\r\n            completeTime: \"2024-01-12 15:45:00\",\r\n            totalPrice: 2000.00,\r\n            settleAmount: 2000.00,\r\n            settleTime: \"2024-01-13 09:30:00\",\r\n            objectStatus: 2,\r\n            payStatus: 2,\r\n          },\r\n        ];\r\n      } else if (currentTabValue === 'rejected') {\r\n        // 审核失败任务\r\n        mockData = [\r\n          {\r\n            id: 5,\r\n            objectName: \"监控系统维修\",\r\n            serverTypeName: \"维修服务\",\r\n            finalCustomer: \"孙七物业\",\r\n            contact: \"孙七\",\r\n            contactPhone: \"13800138005\",\r\n            distributionAddress: \"杭州市西湖区xxx小区\",\r\n            completeTime: \"2024-01-11 13:15:00\",\r\n            totalPrice: 900.00,\r\n            settleAmount: 900.00,\r\n            auditRemark: \"审核失败：费用明细不清晰，请重新提交\",\r\n            objectStatus: 2,\r\n            payStatus: 3,\r\n          },\r\n        ];\r\n      }\r\n\r\n      this.list = [...this.list, ...mockData];\r\n      this.page.total = mockData.length;\r\n    },\r\n    // 滚动到底部加载更多\r\n    scrolltolower() {\r\n      if (this.list.length == this.page.total) return;\r\n      this.page.current++;\r\n      this.getList();\r\n    },\r\n    // Tab切换\r\n    handleTabChange(index) {\r\n      this.currentTab = index;\r\n      this.page.current = 1;\r\n      this.list = [];\r\n      this.getList();\r\n    },\r\n    // 搜索\r\n    handleSearch() {\r\n      this.list = [];\r\n      this.page.current = 1;\r\n      this.getList();\r\n    },\r\n    // 筛选相关方法\r\n    resetFilter() {\r\n      this.filterForm = {\r\n        minAmount: \"\",\r\n        maxAmount: \"\",\r\n        startDate: \"\",\r\n        endDate: \"\",\r\n      };\r\n    },\r\n    applyFilter() {\r\n      this.showFilterPopup = false;\r\n      this.page.current = 1;\r\n      this.list = [];\r\n      this.getList();\r\n    },\r\n    // 获取结算状态\r\n    getSettleStatus(item) {\r\n      if (item.payStatus === null || item.payStatus === undefined) {\r\n        return \"pending\"; // 待结算\r\n      }else{\r\n        return item.payStatus\r\n      }\r\n     \r\n    },\r\n    // 获取结算状态文本\r\n    getSettleStatusText(item) {\r\n    //   const status = this.getSettleStatus(item);\r\n      const statusMap = {\r\n        0: \"待审核\",\r\n        1: \"审核通过\",\r\n        2: \"已付款\",\r\n        3: \"审核失败\",\r\n      };\r\n      return statusMap[item.payStatus] ;\r\n    },\r\n    // 显示任务详情\r\n    showOrderDetail(item) {\r\n      this.currentItem = item;\r\n      this.showDetailPopup = true;\r\n    },\r\n    // 申请结算\r\n    handleApplySettle(item) {\r\n      this.currentItem = item;\r\n      this.settleForm = {\r\n        id: item.id,\r\n        totalPrice: item.totalPrice,\r\n        applyContent: \"\",\r\n        payStatus:0\r\n      };\r\n      this.showSettlePopup = true;\r\n    },\r\n    // 提交结算申请\r\n    submitSettle() {\r\n      if (!this.settleForm.totalPrice) {\r\n        this.$u.toast(\"请输入结算金额\");\r\n        return;\r\n      }\r\n\r\n      this.$u.api.editSettlement(this.settleForm).then(() => {\r\n        this.$u.toast(\"申请成功\");\r\n        this.showSettlePopup = false;\r\n        this.settleForm = {\r\n          id: \"\",\r\n          totalPrice: \"\",\r\n          applyContent: \"\",\r\n        };\r\n        this.refreshData();\r\n      }).catch((err) => {\r\n        this.$u.toast(err.message || \"申请失败\");\r\n      });\r\n    },\r\n    // 拨打电话\r\n    makePhoneCall(phone) {\r\n      uni.makePhoneCall({\r\n        phoneNumber: phone,\r\n        success() {\r\n          console.log(\"拨打电话成功！\");\r\n        },\r\n        fail(err) {\r\n          console.log(\"拨打电话失败！\", err);\r\n        },\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.workbench-wrap {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  padding: 32rpx 0 0 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n// 结算金额统计卡片\r\n.summary-card {\r\n  margin: 32rpx 32rpx 24rpx 32rpx;\r\n  background: #fff;\r\n  border-radius: 24rpx;\r\n  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.06);\r\n  padding: 32rpx 24rpx 24rpx 24rpx;\r\n}\r\n\r\n.summary-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 24rpx;\r\n  color: #333;\r\n}\r\n\r\n.summary-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.stat-item {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 24rpx;\r\n  color: #888;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #2d8cf0;\r\n}\r\n\r\n.stat-item.total .stat-value {\r\n  color: #2d8cf0;\r\n}\r\n\r\n.stat-item.pending .stat-value {\r\n  color: #ff9900;\r\n}\r\n\r\n.stat-item.processing .stat-value {\r\n  color: #19be6b;\r\n}\r\n\r\n.stat-item.finished .stat-value {\r\n  color: #909399;\r\n}\r\n\r\n// 搜索容器\r\n.search-container {\r\n  display: flex;\r\n  align-items: center;\r\n  margin: 0 32rpx 16rpx 32rpx;\r\n  gap: 16rpx;\r\n}\r\n\r\n.search-box {\r\n  flex: 1;\r\n}\r\n\r\n.filter-btn {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 80rpx;\r\n  height: 68rpx;\r\n  background: #fff;\r\n  border-radius: 12rpx;\r\n  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);\r\n\r\n  text {\r\n    font-size: 20rpx;\r\n    color: #666;\r\n    margin-top: 4rpx;\r\n  }\r\n}\r\n\r\n// Tab栏\r\n.order-tab-bar {\r\n  display: flex;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  margin: 0 32rpx 16rpx 32rpx;\r\n  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);\r\n  overflow: hidden;\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 24rpx 0;\r\n  font-size: 28rpx;\r\n  color: #888;\r\n  background: #fff;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #2d8cf0;\r\n  font-weight: bold;\r\n  background: #e6f7ff;\r\n}\r\n\r\n// 任务列表\r\n.order-list {\r\n  margin: 0 32rpx;\r\n}\r\n\r\n.order-card {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.04);\r\n  margin-bottom: 24rpx;\r\n  padding: 24rpx 20rpx 16rpx 20rpx;\r\n  transition: box-shadow 0.2s;\r\n}\r\n\r\n.order-card:hover {\r\n  box-shadow: 0 8rpx 32rpx 0 rgba(45, 140, 240, 0.08);\r\n}\r\n\r\n.order-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.order-title {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.order-status {\r\n  font-size: 24rpx;\r\n  padding: 4rpx 16rpx;\r\n  border-radius: 16rpx;\r\n  background: #f5f7fa;\r\n}\r\n\r\n.order-status.status-2 {\r\n  color: #19be6b;\r\n  background: #e6ffed;\r\n}\r\n\r\n.settle-status {\r\n  font-size: 22rpx;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n.settle-status.settle-status- {\r\n  color: #ff9900;\r\n  background: #fff7e6;\r\n}\r\n\r\n.settle-status.settle-status-0 {\r\n  color: #2d8cf0;\r\n  background: #e6f7ff;\r\n}\r\n\r\n.settle-status.settle-status-1 {\r\n  color: #19be6b;\r\n  background: #e6ffed;\r\n}\r\n\r\n.settle-status.settle-status-2 {\r\n  color: #67c23a;\r\n  background: #f0f9ff;\r\n}\r\n\r\n.settle-status.settle-status-3 {\r\n  color: #f56c6c;\r\n  background: #fef0f0;\r\n}\r\n\r\n.order-detail-row {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  font-size: 26rpx;\r\n  color: #555;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.order-label {\r\n  min-width: 120rpx;\r\n  color: #888;\r\n  font-weight: 400;\r\n}\r\n\r\n.order-value {\r\n  flex: 1;\r\n  color: #333;\r\n  word-break: break-all;\r\n}\r\n\r\n.order-value.price {\r\n  color: #f56c6c;\r\n  font-weight: 600;\r\n}\r\n\r\n.order-value.settle-amount {\r\n  color: #19be6b;\r\n  font-weight: 600;\r\n}\r\n\r\n.order-actions {\r\n  display: flex;\r\n  gap: 24rpx;\r\n  border-top: 1px solid #f0f0f0;\r\n  margin-top: 18rpx;\r\n  padding-top: 18rpx;\r\n  justify-content: flex-end;\r\n  background: #fff;\r\n}\r\n\r\n.action-btn {\r\n  min-width: 120rpx;\r\n  padding: 0 32rpx;\r\n  height: 56rpx;\r\n  line-height: 56rpx;\r\n  border: none;\r\n  border-radius: 32rpx;\r\n  background: #f5f7fa;\r\n  color: #2d8cf0;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  margin: 0;\r\n  outline: none;\r\n  box-shadow: 0 2rpx 8rpx 0 rgba(45, 140, 240, 0.04);\r\n  transition: background 0.2s, color 0.2s;\r\n}\r\n\r\n.action-btn.primary {\r\n  background: linear-gradient(90deg, #2d8cf0 0%, #57a3f3 100%);\r\n  color: #fff;\r\n}\r\n\r\n.action-btn:active {\r\n  opacity: 0.85;\r\n}\r\n\r\n.empty-tip {\r\n  text-align: center;\r\n  color: #bbb;\r\n  font-size: 28rpx;\r\n  margin: 64rpx 0;\r\n}\r\n\r\n// 筛选弹窗\r\n.filter-popup {\r\n  padding: 30rpx;\r\n\r\n  .popup-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    text-align: center;\r\n    margin-bottom: 30rpx;\r\n  }\r\n}\r\n\r\n.filter-content {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.filter-item {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.filter-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.amount-range,\r\n.date-range {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n}\r\n\r\n.filter-footer {\r\n  display: flex;\r\n  gap: 24rpx;\r\n\r\n  .u-button {\r\n    flex: 1;\r\n  }\r\n}\r\n\r\n// 详情弹窗\r\n.detail-popup {\r\n  padding: 30rpx;\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n\r\n  .popup-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    text-align: center;\r\n    margin-bottom: 30rpx;\r\n  }\r\n}\r\n\r\n.detail-content {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 16rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.detail-label {\r\n  min-width: 140rpx;\r\n  color: #888;\r\n  font-weight: 400;\r\n}\r\n\r\n.detail-value {\r\n  flex: 1;\r\n  color: #333;\r\n  word-break: break-all;\r\n}\r\n\r\n.detail-value.price {\r\n  color: #f56c6c;\r\n  font-weight: 600;\r\n}\r\n\r\n.detail-value.settle-amount {\r\n  color: #19be6b;\r\n  font-weight: 600;\r\n}\r\n\r\n.detail-value.reject-reason {\r\n  color: #f56c6c;\r\n  font-weight: 500;\r\n  background: #fef0f0;\r\n  padding: 8rpx 12rpx;\r\n  border-radius: 8rpx;\r\n  border-left: 4rpx solid #f56c6c;\r\n}\r\n\r\n// 申请结算弹窗\r\n.settle-popup {\r\n  padding: 30rpx;\r\n\r\n  .popup-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    text-align: center;\r\n    margin-bottom: 30rpx;\r\n  }\r\n\r\n  .popup-content {\r\n    margin-bottom: 30rpx;\r\n  }\r\n\r\n  .popup-footer {\r\n    padding: 20rpx 0;\r\n  }\r\n}\r\n\r\n::v-deep input {\r\n  text-align: right !important;\r\n}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settle.vue?vue&type=style&index=0&id=362ca366&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settle.vue?vue&type=style&index=0&id=362ca366&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759056994095\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}