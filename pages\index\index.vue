<template>
  <page-meta
    :page-style="
      'overflow:' + (showFinishPopup || showSignDrawer ? 'hidden' : 'visible')
    "
  ></page-meta>
  <page-container
    :show="showFinishPopup || showSignDrawer"
    @beforeleave="
      showFinishPopup = false;
      showSignDrawer = false;
    "
  ></page-container>
  <view class="workbench-wrap">
    <!-- 工单概况统计卡片 -->
    <view class="summary-card">
      <view class="summary-title">工单概况</view>
      <view class="summary-stats">
        <view class="stat-item total">
          <view class="stat-label">工单总数</view>
          <view class="stat-value">{{ summary.totalNum }}</view>
        </view>
        <view class="stat-item pending">
          <view class="stat-label">待接单</view>
          <view class="stat-value">{{ summary.waitNum }}</view>
        </view>
        <view class="stat-item processing">
          <view class="stat-label">进行中</view>
          <view class="stat-value">{{ summary.doNum }}</view>
        </view>
        <view class="stat-item finished">
          <view class="stat-label">已完成</view>
          <view class="stat-value">{{ summary.completeNum }}</view>
        </view>
      </view>
    </view>

    <!-- 工单tab栏 -->
    <view class="order-tab-bar">
      <view
        v-for="(tab, idx) in tabs"
        :key="tab.value"
        :class="['tab-item', { active: currentTab === idx }]"
        @tap="handleClickTab(idx)"
      >
        {{ tab.label }}
      </view>
    </view>

    <!-- 工单列表 -->
    <view class="order-list">
      <view v-if="orders.length === 0" class="empty-tip"> 暂无工单 </view>
      <view
        v-for="order in orders"
        @click.stop="toDetail(order)"
        :key="order.id"
        class="order-card"
      >
        <view class="order-header">
          <view class="order-title">{{ order.objectName }}</view>
          <view style="display: flex; gap: 8rpx">
            <view
              class="order-status"
              :class="'status-' + order.objectStatus"
              >{{ statusMap[order.objectStatus] }}</view
            >
            <view
              v-if="order.isNeedSign == 1 && order.isSign == 0"
              class="order-status status-needSign"
            >
              待签到
            </view>
          </view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">服务类型：</view>
          <view class="order-value">{{ order.serverTypeName }}</view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">服务时间：</view>
          <view class="order-value"
            >{{ order.serviceStartTime }} - {{ order.serviceEndTime }}</view
          >
        </view>
        <!-- <view class="order-detail-row">
          <view class="order-label">工单描述：</view>
          <view class="order-value">{{ order.taskDescription }}</view>
        </view> -->
        <view class="order-detail-row">
          <view class="order-label">客户名称：</view>
          <view class="order-value">{{ order.customerName }}</view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">联系人：</view>
          <view class="order-value">{{ order.contact }}</view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">联系电话：</view>
          <view class="order-value">{{ order.contactPhone }}</view><u-icon 
              v-if="order.contactPhone" 
              name="phone" 
              size="20" 
              color="#2979ff" 
              @tap.stop="makePhoneCall(order.contactPhone)"
              style="margin-left: 10rpx; cursor: pointer;"
            ></u-icon>
        </view>
        <view class="order-detail-row">
          <view class="order-label">地址：</view>
          <view class="order-value">{{ order.distributionAddress }}</view>
        </view>
        <!-- 操作区 -->
        <view class="order-actions">
          <template v-if="order.objectStatus == 3">
            <u-button
              type="primary"
              plain
              class="action-btn primary"
              @tap.stop="handleAccept(order)"
              >接单</u-button
            >
          </template>
          <template>
            <u-button
              class="action-btn"
              type="primary"
              v-if="
                order.objectStatus == 1 &&
                order.isNeedSign == 1 &&
                order.isSign == 0
              "
              icon="map"
              plain
              hairline
              @tap.stop="handleSign(order)"
              >签到</u-button
            >

            <u-button
              class="action-btn primary"
              plain
              type="primary"
              v-if="
                order.isNeedSign == 1
                  ? order.isSign == 1
                  : order.objectStatus == 1
              "
              @tap.stop="handleFinish(order)"
              >完成</u-button
            >
          </template>
        </view>
      </view>
    </view>
    <!-- 签到抽屉 -->
    <u-popup
      :show="showSignDrawer"
      type="bottom"
      closeOnClickOverlay
      @close="showSignDrawer = false"
      :mask-click="true"
      background="#fff"
      style="z-index: 9999"
    >
      <view style="padding: 32rpx 24rpx">
        <view style="font-size: 32rpx; font-weight: bold; margin-bottom: 24rpx"
          >签到</view
        >
        <view style="margin-bottom: 24rpx">
          <view style="font-size: 28rpx; margin-bottom: 8rpx">选择位置</view>
          <u-button
            @tap="chooseLocation"
            type="primary"
            icon="map"
            :loading="locationLoading"
            loadingText="正在获取位置..."
            plain
          >
            {{ signAddress ? signAddress : "点击获取位置" }}
          </u-button>
        </view>
        <view style="margin-bottom: 24rpx">
          <view style="font-size: 28rpx; margin-bottom: 8rpx">上传照片</view>
          <uv-upload
            accept="media"
            @clickPreview="handleClickPreview"
            :fileList="signPhotoUrl"
            @afterRead="afterReadSign"
            @delete="handleDeleteSign"
            multiple
            :maxCount="9"
          >
          </uv-upload>
        </view>
        <!-- <view style="margin-bottom: 24rpx">
          <view style="font-size: 28rpx; margin-bottom: 8rpx">备注</view>
          <u-input
            v-model="signRemark"
            placeholder="请输入备注"
            type="textarea"
            border
          />
        </view> -->
        <u-button type="primary" @tap="submitSign">确认签到</u-button>
      </view>
    </u-popup>

    <!-- 接单 -->
    <u-modal
      :show="acceptOrderModalShow"
      @confirm="acceptOrderConfirm"
      ref="uModal"
      title="确认接单"
      content="确认接单吗？"
      showCancelButton
      @cancel="acceptOrderModalShow = false"
      :asyncClose="true"
    ></u-modal>
    <!-- 完成 -->
    <u-popup
      :show="showFinishPopup"
      mode="bottom"
      @close="showFinishPopup = false"
      :closeable="true"
    >
      <view class="finish-popup">
        <view class="popup-title">完成工单</view>
        <view class="popup-content">
          <u-form
            labelPosition="top"
            labelWidth="auto"
            :model="finishForm"
            ref="finishForm"
          >
            <u-form-item
              borderBottom
              labelPosition="left"
              label="服务开始时间"
              required
            >
              <view
                style="
                  display: flex;
                  justify-content: flex-end;
                  align-items: center;
                "
              >
                <text @click="shiwServiceStartTimePicker = true"
                  >{{
                    dateFormat(
                      new Date(Number(finishForm.serviceStartTime)),
                      "yyyy-MM-dd hh:mm"
                    ) || "青选择服务开始时间"
                  }}
                  <u-icon label="uView" size="40" name="arrow-right"></u-icon
                ></text>
              </view>
              <u-datetime-picker
                v-model="finishForm.serviceStartTime"
                :show="shiwServiceStartTimePicker"
                @cancel="shiwServiceStartTimePicker = false"
                @confirm="shiwServiceStartTimePicker = false"
                mode="datetime"
                :visibleItemCount="5"
              ></u-datetime-picker>
            </u-form-item>
            <u-form-item
              borderBottom
              labelPosition="left"
              label="服务结束时间"
              required
            >
              <view
                style="
                  display: flex;
                  justify-content: flex-end;
                  align-items: center;
                "
              >
                <text @click="shiwServiceEndTimePicker = true"
                  >{{
                    dateFormat(
                      new Date(Number(finishForm.serviceEndTime)),
                      "yyyy-MM-dd hh:mm"
                    ) || "青选择服务结束时间"
                  }}
                  <u-icon label="uView" size="40" name="arrow-right"></u-icon
                ></text>
              </view>
              <u-datetime-picker
                v-model="finishForm.serviceEndTime"
                :show="shiwServiceEndTimePicker"
                @cancel="shiwServiceEndTimePicker = false"
                @confirm="shiwServiceEndTimePicker = false"
                mode="datetime"
                :visibleItemCount="5"
              ></u-datetime-picker>
            </u-form-item>
            <!-- 实际使用工时 -->
            <u-form-item labelPosition="left" label="实际使用工时">
              <u-input
                v-model="finishForm.useTimes"
                placeholder="请输入实际使用工时"
                :border="false"
                type="digit"
              ></u-input>
            </u-form-item>
            <u-form-item labelPosition="left" label="完成情况">
              <dicPicker
                dicUrl="/blade-system/dict-biz/dictionary?code=completeType"
                v-model="finishForm.completeStatus"
                placeholder="请选择完成情况"
              ></dicPicker>
            </u-form-item>
            <u-form-item borderBottom label="现场图">
              <uv-upload
                accept="media"
                @clickPreview="handleClickPreview"
                :fileList="finishForm.workOrderPhotos"
                @afterRead="afterReadForXC"
                @delete="handleDeleteForXC"
                multiple
                :maxCount="9"
              >
              </uv-upload>
            </u-form-item>
            <u-form-item borderBottom label="处理结果图">
              <uv-upload
                accept="media"
                @clickPreview="handleClickPreview"
                :fileList="finishForm.handleResultPhotos"
                @afterRead="afterReadForFinish"
                @delete="handleDeleteForFinish"
                multiple
                :maxCount="9"
              >
              </uv-upload>
            </u-form-item>
            <u-form-item borderBottom label="服务复盘">
              <u-textarea
                v-model="finishForm.serviceReorder"
                border="none"
                placeholder="请输入服务复盘"
              ></u-textarea>
            </u-form-item>
            <u-form-item borderBottom label="备注">
              <u-textarea
                v-model="finishForm.completeRemark"
                border="none"
                placeholder="请输入备注信息"
              ></u-textarea>
            </u-form-item>
          </u-form>
        </view>
        <view class="popup-footer">
          <u-button type="primary" @click="submitFinish">提交</u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import wokerOrderApi from "@/api/wokerOrder.js";
import QQMapWX from "@/utils/qqmap-wx-jssdk.min.js";
import http from "../../http/api.js";
import { dateFormat } from "../../utils/date.js";
import dicPicker from "@/components/dic-picker/dic-picker.vue";
export default {
  data() {
    return {
      showPC: false, // 视图容器控制
      dateFormat,
      summary: {
        totalNum: 0,
        waitNum: 0,
        doNum: 0,
        completeNum: 0,
      },
      tabs: [
        { label: "进行中", value: "processing" },
        { label: "待接单", value: "pending" },
      ],
      currentTab: 0,
      orders: [],
      statusMap: {
        3: "待接单",
        1: "进行中",
        2: "已完成",
      },
      showSignDrawer: false,
      locationLoading: false,
      signAddress: null,
      signPhotoUrl: "",
      signRemark: "",
      signOrder: null,
      // 初始化地图实例
      qqmapsdk: null,

      // 接单
      currentItem: null,
      acceptOrderModalShow: false,

      //完成
      showFinishPopup: false,
      shiwServiceStartTimePicker: false,
      shiwServiceEndTimePicker: false,
      finishForm: {
        serviceStartTime: Number(new Date()),
        serviceEndTime: Number(new Date()),
        completeRemark: "",
        useTimes: null,
        serviceReorder: "",
        workOrderPhotos: [],
        handleResultPhotos: [],
      },
    };
  },
  components: {
    dicPicker,
  },
  computed: {
    filteredOrders() {
      const status = this.tabs[this.currentTab].value;
      return this.orders.filter((o) => o.status === status);
    },
  },
  async onLoad() {
    await this.wxlogin();
    this.fetchOrders();
    this.getTaskStatistics();
    // 实例化，填入你申请到的 Key
    this.qqmapsdk = new QQMapWX({
      key: "V37BZ-5JF63-HBZ3S-34XAJ-KPG2Q-74FPM", // 请替换为你的真实 Key
    });
  },
  onReachBottom() {
    console.log("上拉加载");
    
    this.fetchOrders();
  },
  onPullDownRefresh() {
    this.fetchOrders();
    this.getTaskStatistics();
     uni.stopPullDownRefresh();
  },
  methods: {
    wxlogin() {
      return new Promise((resolve) => {
        uni.login({
          complete: (res) => {
            this.$u.api
              .wxToken({ code: res.code })
              .then((data) => {
                this.$u.func.login(data);
                resolve();
              })
              .catch((err) => {
                this.$u.func.showToast({ title: err });
              });
          },
        });
      });
    },
    handleClickTab(idx) {
      this.currentTab = idx;
      this.fetchOrders();
    },
    async fetchOrders() {
      const map = {
        0: 1,
        1: 3,
      };
      this.$u.api
        .getWorkerOrder({
          pageNum: 1,
          pageSize: 1000,
          objectStatus: map[this.currentTab],
        })
        .then((data) => {
          this.orders = data.data.records || [];
          // this.summary = data.summary || {};
        });
    },
    getTaskStatistics() { 
      this.$u.api.getTaskStatistics().then((data) => {
        this.summary = data.data || {};
      });
    },
    
    //接单
    handleAccept(item) {
      this.currentItem = item;
      this.acceptOrderModalShow = true;
    },
    acceptOrderConfirm() {
      this.$u.api.startWorkerOrder(this.currentItem.id).then((res) => {
        this.acceptOrderModalShow = false;
        this.fetchOrders();
      });
    },
    handleSign(order) {
      this.signOrder = order;
      this.showSignDrawer = true;
      this.signAddress = null;
      this.signPhotoUrl = [];
      this.signRemark = "";
      this.chooseLocation();
    },
    chooseLocation() {
      // wx.chooseLocation({
      //   success: (res) => {
      //     this.signAddress = res;
      //   },
      //   fail: (err) => {
      // 	console.log(err);

      //     uni.showToast({ title: '位置选择失败', icon: 'none' });
      //   }
      // });
      this.locationLoading = true;
      wx.getLocation({
        type: "gcj02",

        success: (res) => {
          console.log(res);

          // 成功获取经纬度后，进行逆地址解析
          this.qqmapsdk.reverseGeocoder({
            location: {
              latitude: res.latitude,
              longitude: res.longitude,
            },
            success: (result) => {
              // 逆解析成功回调
              console.log("逆地址解析结果：", result);
              // 详细的地址信息在 result.result 里
              const addressInfo = result.result.address_component;
              const formattedAddress = result.result.address;
              console.log("所在城市：", addressInfo.city);
              console.log("完整地址：", formattedAddress);
              this.locationLoading = false;
              this.signAddress = formattedAddress;
              // 你可以在这里将地址信息更新到 data 中，或进行其他操作
            },
            fail: function (err) {
              this.locationLoading = false;
              console.error("逆地址解析失败：", err);
              uni.showToast({ title: "位置解析失败", icon: "none" });
            },
          });
        },
        fail: (err) => {
          console.log(err);
          this.locationLoading = false;
          uni.showToast({ title: "位置选择失败", icon: "none" });
        },
      });
    },
    afterReadSign(event) {
      console.log(event);
      const { file } = event;
      const indexAll = this.signPhotoUrl.length;
      file.forEach((item, index) => {
        this.signPhotoUrl.push({
          ...item,
          status: "uploading",
          message: "上传中",
          // url: item.thumb,
          index: indexAll + index,
        });
      });
      file.forEach((item, index) => {
        this.uploadFileSign(item.url, indexAll + index);
      });
    },
    uploadFileSign(url, index) {
      return new Promise((resolve) => {
        const params = {
          filePath: url,
          name: "file",
        };
        http.upload("/blade-resource/attach/upload", params).then((res) => {
          this.signPhotoUrl.find((item) => item.index == index).status = "success";
          this.signPhotoUrl.find((item) => item.index == index).message = "";
          this.signPhotoUrl.find((item) => item.index == index).url =
            res.data.link;
        });
      });
    },
    handleDeleteSign({ file, index, name }) {
      console.log(file, index, name);
      this.signPhotoUrl.splice(index, 1);
    },
    handleClickPreview(url, lists, name) {
      console.log(url, lists, name);
    },
    submitSign() {
      // if (!this.signAddress) {
      //   uni.showToast({ title: "请获取位置", icon: "none" });
      //   return;
      // }
      if (!this.signPhotoUrl) {
        uni.showToast({ title: "请上传照片", icon: "none" });
        return;
      }
      // 这里可以提交签到数据
      const data = {
        id: this.signOrder.id,
        address: this.signAddress,
        signPhotoUrl: this.signPhotoUrl.map((item) => item.url).join(","),
        // remark: this.signRemark,
      };
      this.$u.api
        .signIn(data)
        .then((res) => {
          console.log(res);
        })
        .then(() => {
          uni.showToast({ title: "签到成功", icon: "success" });
          this.showSignDrawer = false;
          this.fetchOrders();
        });
    },

    handleFinish(item) {
      this.showFinishPopup = true;
      this.showPC = true;
      this.currentItem = item;
      this.finishForm.serviceStartTime = Number(new Date(item.serviceStartTime));
      this.finishForm.serviceEndTime = Number(new Date(item.serviceEndTime));
    },
    afterReadForXC(event) {
      console.log(event);
      const { file } = event;
      const indexAll = this.finishForm.workOrderPhotos.length;
      file.forEach((item, index) => {
        this.finishForm.workOrderPhotos.push({
          ...item,
          status: "uploading",
          message: "上传中",
          // url: item.thumb,
          index: indexAll + index,
        });
      });
      file.forEach((item, index) => {
        this.uploadFileForXC(item.url, indexAll + index);
      });
    },
    uploadFileForXC(url, index) {
      return new Promise((resolve) => {
        const params = {
          filePath: url,
          name: "file",
        };
        http.upload("/blade-resource/attach/upload", params).then((res) => {
          this.finishForm.workOrderPhotos.find(
            (item) => item.index == index
          ).status = "success";
          this.finishForm.workOrderPhotos.find(
            (item) => item.index == index
          ).message = "";
          this.finishForm.workOrderPhotos.find(
            (item) => item.index == index
          ).url = res.data.link;
          this.finishForm.workOrderPhotos.find(
            (item) => item.index == index
          ).id = res.data.id;
        });
      });
    },
    handleDeleteForXC({ file, index, name }) {
      console.log(file, index, name);
      this.finishForm.workOrderPhotos.splice(index, 1);
    },

    afterReadForFinish(event) {
      console.log(event);
      const { file } = event;
      const indexAll = this.finishForm.handleResultPhotos.length;
      file.forEach((item, index) => {
        this.finishForm.handleResultPhotos.push({
          ...item,
          status: "uploading",
          message: "上传中",
          // url: item.thumb,
          index: indexAll + index,
        });
      });
      file.forEach((item, index) => {
        this.uploadFileForFinish(item.url, indexAll + index);
      });
    },
    uploadFileForFinish(url, index) {
      return new Promise((resolve) => {
        const params = {
          filePath: url,
          name: "file",
        };
        http.upload("/blade-resource/attach/upload", params).then((res) => {
          this.finishForm.handleResultPhotos.find(
            (item) => item.index == index
          ).status = "success";
          this.finishForm.handleResultPhotos.find(
            (item) => item.index == index
          ).message = "";
          this.finishForm.handleResultPhotos.find(
            (item) => item.index == index
          ).url = res.data.link;
          this.finishForm.handleResultPhotos.find(
            (item) => item.index == index
          ).id = res.data.id;
        });
      });
    },
    handleDeleteForFinish({ file, index, name }) {
      console.log(file, index, name);
      this.finishForm.handleResultPhotos.splice(index, 1);
    },

    submitFinish() {
      if (!this.finishForm.serviceStartTime || !this.finishForm.serviceEndTime) {
        this.$u.toast("开始时间和结束时间不能为空");
        return;
      }
      const formData = {
        id: this.currentItem.id,
        serviceStartTime: this.dateFormat(
          new Date(Number(this.finishForm.serviceStartTime)),
          "yyyy-MM-dd hh:mm:ss"
        ),
        serviceEndTime: this.dateFormat(
          new Date(Number(this.finishForm.serviceEndTime)),
          "yyyy-MM-dd hh:mm:ss"
        ),
        completeRemark: this.finishForm.completeRemark,
        // completeFiles:
        //   this.finishForm.fileList &&
        //   this.finishForm.fileList.map((item) => item.id).join(","),
        handleResultPhotos:
          this.finishForm.handleResultPhotos &&
          this.finishForm.handleResultPhotos.map((item) => item.id).join(","),
        workOrderPhotos:
          this.finishForm.workOrderPhotos &&
          this.finishForm.workOrderPhotos.map((item) => item.id).join(","),
        useTimes: this.finishForm.useTimes,
        completeStatus: this.finishForm.completeStatus,
        serviceReorder: this.finishForm.serviceReorder,
      };
      this.$u.api
        .finishWorkerOrder(formData)
        .then((res) => {
          this.$u.toast("提交成功");
          this.showFinishPopup = false;
          this.finishForm = {
            finishTime: "",
            completeRemark: "",
            useTimes: null,
            serviceReorder: "",
            workOrderPhotos: [],
            handleResultPhotos: [],
          };
          this.fetchOrders();
        })
        .catch((err) => {
          this.$u.toast(err.message || "提交失败");
        });
    },
    toDetail(item) {
      uni.navigateTo({
        url: "/pages/wokerOrder/wokerOrderDetail?id=" + item.id,
      });
    },
    makePhoneCall(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
        success() {
          console.log("拨打电话成功！");
        },
        fail(err) {
          console.log("拨打电话失败！", err);
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.workbench-wrap {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 32rpx 0 0 0;
  box-sizing: border-box;
}
.summary-card {
  margin: 32rpx 32rpx 24rpx 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.06);
  padding: 32rpx 24rpx 24rpx 24rpx;
}
.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #333;
}
.summary-stats {
  display: flex;
  justify-content: space-between;
}
.stat-item {
  flex: 1;
  text-align: center;
}
.stat-label {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 8rpx;
}
.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d8cf0;
}
.stat-item.total .stat-value {
  color: #2d8cf0;
}
.stat-item.pending .stat-value {
  color: #ff9900;
}
.stat-item.processing .stat-value {
  color: #19be6b;
}
.stat-item.finished .stat-value {
  color: #909399;
}

.order-tab-bar {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin: 0 32rpx 16rpx 32rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);
  overflow: hidden;
}
.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #888;
  background: #fff;
  transition: all 0.2s;
}
.tab-item.active {
  color: #2d8cf0;
  font-weight: bold;
  background: #e6f7ff;
}

.order-list {
  margin: 0 32rpx;
}
.order-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.04);
  margin-bottom: 24rpx;
  padding: 24rpx 20rpx 16rpx 20rpx;
  transition: box-shadow 0.2s;
}
.order-actions {
  display: flex;
  gap: 24rpx;
  border-top: 1px solid #f0f0f0;
  margin-top: 18rpx;
  padding-top: 18rpx;
  justify-content: flex-end;
  background: #fff;
}
.action-btn {
  min-width: 120rpx;
  padding: 0 32rpx;
  height: 56rpx;

  line-height: 56rpx;
  border: none;
  border-radius: 32rpx;
  background: #f5f7fa;
  color: #2d8cf0;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0;
  outline: none;
  box-shadow: 0 2rpx 8rpx 0 rgba(45, 140, 240, 0.04);
  transition: background 0.2s, color 0.2s;
}
.action-btn.primary {
  background: linear-gradient(90deg, #2d8cf0 0%, #57a3f3 100%);
  color: #fff;
}
.action-btn:active {
  opacity: 0.85;
}
.order-card:hover {
  box-shadow: 0 8rpx 32rpx 0 rgba(45, 140, 240, 0.08);
}
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.order-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.order-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
  background: #f5f7fa;
}
.order-status.status-needsign {
  color: #fff;
  background: #ff9900;
}
.order-status.status-1 {
  color: #19be6b;
  background: #e6ffed;
}
.order-status.status-2 {
  color: #909399;
  background: #f4f4f5;
}
.order-detail-row {
  display: flex;
  align-items: flex-start;
  font-size: 26rpx;
  color: #555;
  margin-bottom: 8rpx;
}
.order-label {
  min-width: 120rpx;
  color: #888;
  font-weight: 400;
}
.order-value {
  flex: 1;
  color: #333;
  word-break: break-all;
}
.empty-tip {
  text-align: center;
  color: #bbb;
  font-size: 28rpx;
  margin: 64rpx 0;
}
.finish-popup {
  height: 80vh;
  overflow-y: auto;
  position: relative;
  .popup-title {
    padding-top: 20rpx;
    position: sticky;
    background-color: #fff;
    top: 0;
    z-index: 10;
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    // margin-bottom: 30rpx;
  }

  .popup-content {
    padding: 30rpx;
    margin-bottom: 30rpx;
  }

  .popup-footer {
    padding: 20rpx 0;
    position: fixed;
    bottom: 0;
    left: 20rpx;
    right: 20rpx;
    width: auto;
  }
}
::v-deep input {
  text-align: right !important;
}
</style>
