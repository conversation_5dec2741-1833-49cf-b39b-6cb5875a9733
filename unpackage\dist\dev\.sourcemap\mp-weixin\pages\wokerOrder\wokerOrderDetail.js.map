{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrderDetail.vue?0e39", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrderDetail.vue?e7c8", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrderDetail.vue?555e", "uni-app:///pages/wokerOrder/wokerOrderDetail.vue", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrderDetail.vue?d830", "webpack:///D:/project/vt-unih5-order/pages/wokerOrder/wokerOrderDetail.vue?81b3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "dicPicker", "data", "detailForm", "objectStatus", "customerName", "contact", "contactPhone", "distributionAddress", "objectName", "serverTypeName", "serviceStartTime", "serviceEndTime", "projectLeaderName", "handleUserName", "SLATypeName", "taskDescription", "fileList", "milestoneVOList", "handleContent", "createTime", "sealContractObjectResultVOList", "handleName", "useTimes", "completeStatus", "completeStatusName", "signTime", "signAddress", "serviceReorder", "completeRemark", "signPhotoList", "link", "workOrderPhotoList", "handleResultPhotoList", "dateFormat", "report", "workOrderPhotos", "url", "handleResultPhotos", "showSignDrawer", "locationLoading", "signPhotoUrl", "signRemark", "qqmapsdk", "acceptOrderModalShow", "showFinishPopup", "shiwServiceStartTimePicker", "shiwServiceEndTimePicker", "finishForm", "expandedSections", "computed", "showActionButtons", "onLoad", "key", "methods", "getWorkerOrderDetail", "item", "console", "callPhone", "uni", "phoneNumber", "success", "fail", "handleAccept", "acceptOrderConfirm", "startWorkerOrder", "then", "catch", "handleSign", "chooseLocation", "type", "location", "latitude", "longitude", "title", "icon", "afterReadSign", "file", "status", "message", "index", "uploadFileSign", "filePath", "http", "res", "resolve", "handleDeleteSign", "handleClickPreview", "submitSign", "id", "address", "signIn", "handleFinish", "afterReadForXC", "uploadFileForXC", "handleDeleteForXC", "afterReadFor<PERSON>inish", "uploadFileForFinish", "handleDeleteForFinish", "submit<PERSON><PERSON>sh", "finishWorkerOrder", "getCompleteStatusText", "getCompleteStatusClass", "previewImage", "current", "urls", "toggleSection", "getExpandedState"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACqC;;;AAGpG;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpMA;AAAA;AAAA;AAAA;AAA6mB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC0lBjoB;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;QACA;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC,kBACA;UACAC;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAC,iCACA;UACAC;UACAX;UACAC;UACAW;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC,gBACA;YAAAC;UAAA,GACA;YAAAA;UAAA,EACA;UACAC,qBACA;YAAAD;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,EACA;UACAE,wBACA;YAAAF;UAAA,GACA;YAAAA;UAAA;QAEA,GACA;UACAT;UACAX;UACAC;UACAW;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC,gBACA;YAAAC;UAAA,EACA;UACAC,qBACA;YAAAD;UAAA,EACA;UACAE,wBACA;YAAAF;UAAA;QAEA;MAEA;MACAG;MACAC;QACArB;QACAH;QACAC;QACAW;QACAC;QAAA;QACAI,gBACA;QACAC,gBACA;QACAO,kBACA;UACAC;QACA,GACA;UACAA;QACA,GACA;UACAA;QACA,EACA;QACAC,qBACA;UACAD;QACA,GACA;UACAA;QACA;MAEA;MAEA;MACAE;MACAC;MACAb;MACAc;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;QACArC;QACAC;QACAiB;QACAN;QACAK;QACAQ;QACAE;QACAd;MACA;MAEA;MACAyB;IACA;EACA;EACAC;IACA;IACAC;MACA;;MAEA;MACA,OACA;IAEA;IACA9B;MACA;IACA;EACA;EACA+B;IAAA;IACA;MACA;IACA;IACA;IACA;MACAC;IACA;EACA;;EAEAC;IACAC;MAAA;MACA;QACA;QACA;UACA,oCACA;YACA,uCACAC;cACAnB;YAAA;UAEA;QACA;QAEAoB;MACA;IACA;IAEA;IACAC;MACAC;QACAC;QACAC;UACAJ;QACA;QACAK;UACAL;QACA;MACA;IACA;IAEA;IACAM;MACA;IACA;IAEAC;MAAA;MACA,YACAC,qCACAC;QACA;QACA;QACA;QACA;MACA,GACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MAAA;MACA;MACA3E;QACA4E;QACAT;UACAJ;UACA;UACA;YACAc;cACAC;cACAC;YACA;YACAZ;cACA;cACAJ;cACA;cACA;cACAA;cACAA;cACA;cACA;YACA;YACAK;cACA;cACAL;cACAE;gBAAAe;gBAAAC;cAAA;YACA;UACA;QACA;QACAb;UACAL;UACA;UACAE;YAAAe;YAAAC;UAAA;QACA;MACA;IACA;IAEAC;MAAA;MACAnB;MACA;MACA;MACAoB;QACA,yDACArB;UACAsB;UACAC;UACAC;QAAA,GACA;MACA;MACAH;QACA;MACA;IACA;IAEAI;MAAA;MACA;QACA;UACAC;UACAnF;QACA;QACAoF;UACA;YAAA;UAAA,YACA;UACA;YAAA;UAAA;UACA;YAAA;UAAA,SACAC;UACAC;QACA;MACA;IACA;IAEAC;MAAA;QAAAN;QAAAjF;MACA0D;MACA;IACA;IAEA8B;MACA9B;IACA;IAEA+B;MAAA;MACA;QACA7B;UAAAe;UAAAC;QAAA;QACA;MACA;MACA;MACA;QACAc;QACAC;QACAjD;UAAA;QAAA;MACA;MACA,YACAkD,aACAzB;QACAT;QACAE;UAAAe;UAAAC;QAAA;QACA;QACA;QACA;MACA,GACAR;QACA;MACA;IACA;IAEA;IACAyB;MACA;MACA,0CACA,2CACA;MACA,wCACA,yCACA;IACA;IAEAC;MAAA;MACApC;MACA;MACA;MACAoB;QACA,uEACArB;UACAsB;UACAC;UACAC;QAAA,GACA;MACA;MACAH;QACA;MACA;IACA;IAEAiB;MAAA;MACA;QACA;UACAZ;UACAnF;QACA;QACAoF;UACA,uCACA;YAAA;UAAA,EACA;UACA,uCACA;YAAA;UAAA,EACA;UACA,uCACA;YAAA;UAAA,EACA;UACA,uCACA;YAAA;UAAA,EACA;UACAE;QACA;MACA;IACA;IAEAU;MAAA;QAAAf;QAAAjF;MACA0D;MACA;IACA;IAEAuC;MAAA;MACAvC;MACA;MACA;MACAoB;QACA,0EACArB;UACAsB;UACAC;UACAC;QAAA,GACA;MACA;MACAH;QACA;MACA;IACA;IAEAoB;MAAA;MACA;QACA;UACAf;UACAnF;QACA;QACAoF;UACA,2CACA;YAAA;UAAA,EACA;UACA,2CACA;YAAA;UAAA,EACA;UACA,2CACA;YAAA;UAAA,EACA;UACA,2CACA;YAAA;UAAA,EACA;UACAE;QACA;MACA;IACA;IAEAa;MAAA;QAAAlB;QAAAjF;MACA0D;MACA;IACA;IAEA0C;MAAA;MACA,IACA,qCACA,iCACA;QACA;QACA;MACA;MACA;QACAV;QACA9E,kCACA,oDACA,sBACA;QACAC,gCACA,kDACA,sBACA;QACAiB;QACAS,oBACA,sCACA;UAAA;QAAA;QACAF,iBACA,mCACA;UAAA;QAAA;QACAb;QACAC;QACAI;MACA;MACA,YACAwE,4BACAlC;QACA;QACA;QACA;UACAvD;UACAC;UACAiB;UACAN;UACAK;UACAQ;UACAE;UACAd;QACA;QACA;QACA;MACA,GACA2C;QACA;MACA;IACA;IAEA;IACAkC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACA5C;QACA6C;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3nCA;AAAA;AAAA;AAAA;AAAw5B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;ACA56B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/wokerOrder/wokerOrderDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/wokerOrder/wokerOrderDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./wokerOrderDetail.vue?vue&type=template&id=41762ff7&scoped=true&\"\nvar renderjs\nimport script from \"./wokerOrderDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./wokerOrderDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./wokerOrderDetail.vue?vue&type=style&index=0&id=41762ff7&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"41762ff7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/wokerOrder/wokerOrderDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrderDetail.vue?vue&type=template&id=41762ff7&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-modal/u-modal\" */ \"@/uni_modules/uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uvUpload: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-upload/components/uv-upload/uv-upload\" */ \"@/uni_modules/uv-upload/components/uv-upload/uv-upload.vue\"\n      )\n    },\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker\" */ \"@/uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uTextarea: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-textarea/u-textarea\" */ \"@/uni_modules/uview-ui/components/u-textarea/u-textarea.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 =\n    _vm.detailForm.objectStatus == 2 || _vm.detailForm.objectStatus == 3\n      ? _vm.__map(\n          _vm.detailForm.sealContractObjectResultVOList,\n          function (report, index) {\n            var $orig = _vm.__get_orig(report)\n            var m0 =\n              _vm.detailForm.isNeedSign == 1\n                ? _vm.getExpandedState(\"signin\", index)\n                : null\n            var m1 =\n              _vm.detailForm.isNeedSign == 1\n                ? _vm.getExpandedState(\"signin\", index)\n                : null\n            var g0 =\n              _vm.detailForm.isNeedSign == 1\n                ? report.signPhotoUrl && report.signPhotoUrl.length > 0\n                : null\n            var l0 =\n              _vm.detailForm.isNeedSign == 1 && g0\n                ? report.signPhotoUrl.split(\",\")\n                : null\n            var m2 = _vm.getExpandedState(\"complete\", index)\n            var m3 = _vm.getExpandedState(\"complete\", index)\n            var g1 =\n              report.workOrderPhotoList && report.workOrderPhotoList.length > 0\n            var g2 =\n              report.handleResultPhotoList &&\n              report.handleResultPhotoList.length > 0\n            var g3 = _vm.detailForm.sealContractObjectResultVOList.length\n            return {\n              $orig: $orig,\n              m0: m0,\n              m1: m1,\n              g0: g0,\n              l0: l0,\n              m2: m2,\n              m3: m3,\n              g1: g1,\n              g2: g2,\n              g3: g3,\n            }\n          }\n        )\n      : null\n  var m4 =\n    _vm.dateFormat(\n      new Date(Number(_vm.finishForm.serviceStartTime)),\n      \"yyyy-MM-dd hh:mm\"\n    ) || \"请选择服务开始时间\"\n  var m5 =\n    _vm.dateFormat(\n      new Date(Number(_vm.finishForm.serviceEndTime)),\n      \"yyyy-MM-dd hh:mm\"\n    ) || \"请选择服务结束时间\"\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showFinishPopup = false\n      _vm.showSignDrawer = false\n    }\n    _vm.e1 = function ($event, photo, report) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        photo = _temp2.photo,\n        report = _temp2.report\n      var _temp, _temp2\n      _vm.previewImage(\n        photo,\n        report.signPhotoUrl.split(\",\").map(function (item) {\n          return {\n            link: item,\n          }\n        })\n      )\n    }\n    _vm.e2 = function ($event) {\n      _vm.acceptOrderModalShow = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.showSignDrawer = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.showFinishPopup = false\n    }\n    _vm.e5 = function ($event) {\n      _vm.shiwServiceStartTimePicker = true\n    }\n    _vm.e6 = function ($event) {\n      _vm.shiwServiceStartTimePicker = false\n    }\n    _vm.e7 = function ($event) {\n      _vm.shiwServiceStartTimePicker = false\n    }\n    _vm.e8 = function ($event) {\n      _vm.shiwServiceEndTimePicker = true\n    }\n    _vm.e9 = function ($event) {\n      _vm.shiwServiceEndTimePicker = false\n    }\n    _vm.e10 = function ($event) {\n      _vm.shiwServiceEndTimePicker = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n        m4: m4,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrderDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrderDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n  <page-meta\r\n    :page-style=\"\r\n      'overflow:' + (showFinishPopup || showSignDrawer ? 'hidden' : 'visible')\r\n    \"\r\n  ></page-meta>\r\n  <page-container\r\n    :show=\"showFinishPopup || showSignDrawer\"\r\n    @beforeleave=\"\r\n      showFinishPopup = false;\r\n      showSignDrawer = false;\r\n    \"\r\n  ></page-container>\r\n  <view>\r\n    <view class=\"order-detail\">\r\n      <!-- 服务客户信息 -->\r\n      <view class=\"card shadow\">\r\n        <view class=\"card-header\">\r\n          <view class=\"header-icon\">\r\n            <uni-icons type=\"person\" size=\"20\" color=\"#000\" />\r\n          </view>\r\n          <text class=\"card-title\">服务客户信息</text>\r\n        </view>\r\n        <view class=\"card-content\">\r\n          <view class=\"info-row\">\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">客户名称</text>\r\n              <text class=\"value\">{{ detailForm.customerName }}</text>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">联系人</text>\r\n              <text class=\"value\">{{ detailForm.contact }}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"info-row\">\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">联系电话</text>\r\n              <view style=\"display: flex; align-items: center\"\r\n                ><text class=\"value phone\">{{ detailForm.contactPhone }}</text>\r\n                <u-icon\r\n                  name=\"phone\"\r\n                  color=\"#2979ff\"\r\n                  size=\"20\"\r\n                  @click=\"callPhone(detailForm.contactPhone)\"\r\n                  v-if=\"detailForm.contactPhone\"\r\n                ></u-icon\r\n              ></view>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">地址</text>\r\n              <text class=\"value\">{{ detailForm.distributionAddress }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 任务信息 -->\r\n      <view class=\"card shadow\">\r\n        <view class=\"card-header\">\r\n          <view class=\"header-icon\">\r\n            <uni-icons type=\"compose\" size=\"20\" color=\"#000\" />\r\n          </view>\r\n          <text class=\"card-title\">任务信息</text>\r\n        </view>\r\n        <view class=\"card-content\">\r\n          <view class=\"info-row\">\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">任务名称</text>\r\n              <text class=\"value tag\">{{ detailForm.objectName }}</text>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">服务类型</text>\r\n              <text class=\"value tag\">{{ detailForm.serverTypeName }}</text>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"info-row single\">\r\n            <view class=\"info-item full\">\r\n              <text class=\"label\">服务时间</text>\r\n              <text class=\"value time\"\r\n                >{{ detailForm.serviceStartTime }} -\r\n                {{ detailForm.serviceEndTime }}</text\r\n              >\r\n            </view>\r\n          </view>\r\n          <view class=\"info-row\">\r\n            <view class=\"info-item\">\r\n              <text class=\"label\">发布人</text>\r\n              <text class=\"value\">{{ detailForm.projectLeaderName }}</text>\r\n            </view>\r\n            <view class=\"info-item full\">\r\n              <text class=\"label\">指派工程师</text>\r\n              <text class=\"value\">{{ detailForm.handleUserName }}</text>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"info-row single\">\r\n            <view class=\"info-item full\">\r\n              <text class=\"label\">SLA</text>\r\n              <text class=\"value code\">{{\r\n                detailForm.SLATypeName || \"-\"\r\n              }}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"info-row single\">\r\n            <view class=\"info-item full\">\r\n              <text class=\"label\">任务描述</text>\r\n              <text class=\"value desc\">{{ detailForm.taskDescription }}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"info-row single\">\r\n            <view class=\"info-item full\" v-for=\"item in detailForm.fileList\">\r\n              <text class=\"label\">任务附件</text>\r\n              <text\r\n                class=\"value file-link\"\r\n                v-for=\"item in detailForm.fileList\"\r\n                >{{ item.originalName }}</text\r\n              >\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 任务执行 -->\r\n      <view class=\"card shadow\">\r\n        <view class=\"card-header\">\r\n          <view class=\"header-icon\">\r\n            <uni-icons type=\"calendar\" size=\"20\" color=\"#000\" />\r\n          </view>\r\n          <text class=\"card-title\">任务执行</text>\r\n        </view>\r\n        <view class=\"timeline-container\">\r\n          <view class=\"timeline\">\r\n            <view\r\n              class=\"timeline-item\"\r\n              v-for=\"(item, index) in detailForm.milestoneVOList\"\r\n            >\r\n              <view class=\"timeline-dot active\"></view>\r\n              <view class=\"timeline-line\"></view>\r\n              <view class=\"timeline-content\">\r\n                <view class=\"timeline-header\">\r\n                  <text class=\"timeline-title\">{{ item.handleContent }}</text>\r\n                  <text class=\"timeline-time\">{{ item.createTime }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 完工报告 -->\r\n      <view class=\"card shadow\" v-if=\"detailForm.objectStatus == 2 || detailForm.objectStatus == 3\">\r\n        <view class=\"card-header\">\r\n          <view class=\"header-icon\">\r\n            <uni-icons type=\"checkmarkempty\" size=\"20\" color=\"#000\" />\r\n          </view>\r\n          <text class=\"card-title\">完工报告</text>\r\n        </view>\r\n        <view class=\"card-content\">\r\n          <view\r\n            class=\"completion-report\"\r\n            v-for=\"(report, index) in detailForm.sealContractObjectResultVOList\"\r\n            :key=\"index\"\r\n          >\r\n            <!-- 报告人员信息 -->\r\n            <view class=\"report-header\">\r\n              <text class=\"report-title\">{{ report.handleName || `工程师 ${index + 1}` }}</text>\r\n              <text class=\"report-time\">{{ report.serviceStartTime || '-' }}</text>\r\n            </view>\r\n\r\n            <!-- 签到信息折叠面板 -->\r\n            <view class=\"collapsible-section\" v-if=\"detailForm.isNeedSign == 1\">\r\n              <view\r\n                class=\"section-header\"\r\n                @click=\"toggleSection('signin', index)\"\r\n              >\r\n                <view class=\"section-title\">\r\n                  <uni-icons type=\"location\" size=\"16\" color=\"#2979ff\" />\r\n                  <text class=\"section-text\">签到信息</text>\r\n                </view>\r\n                <view class=\"section-arrow\" :class=\"{ 'expanded': getExpandedState('signin', index) }\">\r\n                  <uni-icons type=\"arrowdown\" size=\"14\" color=\"#666\" />\r\n                </view>\r\n              </view>\r\n\r\n              <view\r\n                class=\"section-content\"\r\n                v-show=\"getExpandedState('signin', index)\"\r\n              >\r\n                <view class=\"info-row\">\r\n                  <view class=\"info-item\">\r\n                    <text class=\"label\">签到时间</text>\r\n                    <text class=\"value time\">{{ report.signTime || report.serviceStartTime || '-' }}</text>\r\n                  </view>\r\n                  <view class=\"info-item\">\r\n                    <text class=\"label\">签到地址</text>\r\n                    <text class=\"value\">{{ report.signAddress || '现场签到' }}</text>\r\n                  </view>\r\n                </view>\r\n\r\n                <!-- 签到图片 -->\r\n                <view class=\"info-row single\" v-if=\"report.signPhotoUrl && report.signPhotoUrl.length > 0\">\r\n                  <view class=\"info-item full\">\r\n                    <text class=\"label\">签到图片</text>\r\n                    <view class=\"photo-grid\">\r\n                      <view\r\n                        class=\"photo-item\"\r\n                        v-for=\"(photo, photoIndex) in report.signPhotoUrl.split(',')\"\r\n                        :key=\"photoIndex\"\r\n                        @click=\"previewImage(photo, report.signPhotoUrl.split(',').map(item => ({link:item})))\"\r\n                      >\r\n                        <image\r\n                          :src=\"photo\"\r\n                          mode=\"aspectFill\"\r\n                          class=\"photo-image\"\r\n                        ></image>\r\n                      </view>\r\n                    </view>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n\r\n            <!-- 完成信息折叠面板 -->\r\n            <view class=\"collapsible-section\">\r\n              <view\r\n                class=\"section-header\"\r\n                @click=\"toggleSection('complete', index)\"\r\n              >\r\n                <view class=\"section-title\">\r\n                  <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#4CAF50\" />\r\n                  <text class=\"section-text\">完成信息</text>\r\n                </view>\r\n                <view class=\"section-arrow\" :class=\"{ 'expanded': getExpandedState('complete', index) }\">\r\n                  <uni-icons type=\"arrowdown\" size=\"14\" color=\"#666\" />\r\n                </view>\r\n              </view>\r\n\r\n              <view\r\n                class=\"section-content\"\r\n                v-show=\"getExpandedState('complete', index)\"\r\n              >\r\n                <view class=\"info-row\">\r\n              <view class=\"info-item\">\r\n                <text class=\"label\">服务开始时间</text>\r\n                <text class=\"value time\">{{\r\n                  report.serviceStartTime || \"-\"\r\n                }}</text>\r\n              </view>\r\n              <view class=\"info-item\">\r\n                <text class=\"label\">服务结束时间</text>\r\n                <text class=\"value time\">{{\r\n                  report.serviceEndTime || \"-\"\r\n                }}</text>\r\n              </view>\r\n            </view>\r\n\r\n            <view class=\"info-row\">\r\n              <view class=\"info-item\">\r\n                <text class=\"label\">实际工时</text>\r\n                <text class=\"value\">{{ report.useTimes || \"-\" }}小时</text>\r\n              </view>\r\n              <view class=\"info-item\">\r\n                <text class=\"label\">完成情况</text>\r\n                <text\r\n                  class=\"value tag\"\r\n                  \r\n                  >{{ report.completeStatusName || \"-\" }}</text\r\n                >\r\n              </view>\r\n            </view>\r\n\r\n            <view class=\"info-row single\" v-if=\"report.serviceReorder\">\r\n              <view class=\"info-item full\">\r\n                <text class=\"label\">服务复盘</text>\r\n                <text class=\"value desc\">{{ report.serviceReorder }}</text>\r\n              </view>\r\n            </view>\r\n\r\n            <view class=\"info-row single\" v-if=\"report.completeRemark\">\r\n              <view class=\"info-item full\">\r\n                <text class=\"label\">备注</text>\r\n                <text class=\"value desc\">{{ report.completeRemark }}</text>\r\n              </view>\r\n            </view>\r\n\r\n            <!-- 现场图片 -->\r\n            <view\r\n              class=\"info-row single\"\r\n              v-if=\"report.workOrderPhotoList && report.workOrderPhotoList.length > 0\"\r\n            >\r\n              <view class=\"info-item full\">\r\n                <text class=\"label\">现场图片</text>\r\n                <view class=\"photo-grid\">\r\n                  <view\r\n                    class=\"photo-item\"\r\n                    v-for=\"(photo, photoIndex) in report.workOrderPhotoList\"\r\n                    :key=\"photoIndex\"\r\n                    @click=\"previewImage(photo.url, report.workOrderPhotoList)\"\r\n                  >\r\n                    <image\r\n                      :src=\"photo.link\"\r\n                      mode=\"aspectFill\"\r\n                      class=\"photo-image\"\r\n                    ></image>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n\r\n            <!-- 处理结果图片 -->\r\n            <view\r\n              class=\"info-row single\"\r\n              v-if=\"\r\n                report.handleResultPhotoList &&\r\n                report.handleResultPhotoList.length > 0\r\n              \"\r\n            >\r\n              <view class=\"info-item full\">\r\n                <text class=\"label\">处理结果图片</text>\r\n                <view class=\"photo-grid\">\r\n                  <view\r\n                    class=\"photo-item\"\r\n                    v-for=\"(photo, photoIndex) in report.handleResultPhotoList\"\r\n                    :key=\"photoIndex\"\r\n                    @click=\"previewImage(photo.url, report.handleResultPhotoList)\"\r\n                  >\r\n                    <image\r\n                      :src=\"photo.link\"\r\n                      mode=\"aspectFill\"\r\n                      class=\"photo-image\"\r\n                    ></image>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n\r\n              </view>\r\n            </view>\r\n\r\n            <!-- 分隔线，如果不是最后一个报告 -->\r\n            <view\r\n              class=\"report-divider\"\r\n              v-if=\"index < detailForm.sealContractObjectResultVOList.length - 1\"\r\n            ></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 底部占位区域，防止内容被固定按钮遮挡 -->\r\n      <view class=\"bottom-placeholder\" v-if=\"showActionButtons\"></view>\r\n    </view>\r\n\r\n    <!-- 固定在底部的操作区 -->\r\n    <view class=\"fixed-action-bar\" v-if=\"showActionButtons\">\r\n      <view class=\"action-buttons\">\r\n        <!-- 接单按钮 -->\r\n        <template v-if=\"detailForm.objectStatus == 3\">\r\n          <u-button\r\n            type=\"primary\"\r\n            class=\"action-btn primary\"\r\n            @tap=\"handleAccept\"\r\n            plain\r\n            >接单</u-button\r\n          >\r\n        </template>\r\n\r\n        <!-- 签到和完成按钮 -->\r\n        <template v-else>\r\n          <!-- 签到按钮 -->\r\n          <u-button\r\n            class=\"action-btn\"\r\n            type=\"primary\"\r\n            v-if=\"\r\n              detailForm.objectStatus == 1 &&\r\n              detailForm.isNeedSign == 1 &&\r\n              detailForm.isSign == 0\r\n            \"\r\n            icon=\"map\"\r\n            plain\r\n            @tap=\"handleSign\"\r\n            >签到</u-button\r\n          >\r\n\r\n          <!-- 完成按钮 -->\r\n          <u-button\r\n            class=\"action-btn primary\"\r\n            type=\"primary\"\r\n            plain\r\n            v-if=\"\r\n              detailForm.isNeedSign == 1\r\n                ? detailForm.isSign == 1\r\n                : detailForm.objectStatus == 1\r\n            \"\r\n            @tap=\"handleFinish\"\r\n            >完成</u-button\r\n          >\r\n        </template>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 接单确认模态框 -->\r\n    <u-modal\r\n      :show=\"acceptOrderModalShow\"\r\n      @confirm=\"acceptOrderConfirm\"\r\n      ref=\"uModal\"\r\n      title=\"确认接单\"\r\n      content=\"确认接单吗？\"\r\n      showCancelButton\r\n      @cancel=\"acceptOrderModalShow = false\"\r\n      :asyncClose=\"true\"\r\n    ></u-modal>\r\n\r\n    <!-- 签到抽屉 -->\r\n    <u-popup\r\n      :show=\"showSignDrawer\"\r\n      mode=\"bottom\"\r\n      closeOnClickOverlay\r\n      @close=\"showSignDrawer = false\"\r\n      :mask-click=\"true\"\r\n      background=\"#fff\"\r\n      style=\"z-index: 9999\"\r\n    >\r\n      <view style=\"padding: 32rpx 24rpx\">\r\n        <view style=\"font-size: 32rpx; font-weight: bold; margin-bottom: 24rpx\"\r\n          >签到</view\r\n        >\r\n        <view style=\"margin-bottom: 24rpx\">\r\n          <view style=\"font-size: 28rpx; margin-bottom: 8rpx\">选择位置</view>\r\n          <u-button\r\n            @tap=\"chooseLocation\"\r\n            type=\"primary\"\r\n            icon=\"map\"\r\n            :loading=\"locationLoading\"\r\n            loadingText=\"正在获取位置...\"\r\n            plain\r\n          >\r\n            {{ signAddress ? signAddress : \"点击获取位置\" }}\r\n          </u-button>\r\n        </view>\r\n        <view style=\"margin-bottom: 24rpx\">\r\n          <view style=\"font-size: 28rpx; margin-bottom: 8rpx\">上传照片</view>\r\n          <uv-upload\r\n            accept=\"media\"\r\n            @clickPreview=\"handleClickPreview\"\r\n            :fileList=\"signPhotoUrl\"\r\n            @afterRead=\"afterReadSign\"\r\n            @delete=\"handleDeleteSign\"\r\n            multiple\r\n            :maxCount=\"9\"\r\n          >\r\n          </uv-upload>\r\n        </view>\r\n        <u-button type=\"primary\" @tap=\"submitSign\">确认签到</u-button>\r\n      </view>\r\n    </u-popup>\r\n\r\n    <!-- 完成任务弹窗 -->\r\n    <u-popup\r\n      :show=\"showFinishPopup\"\r\n      mode=\"bottom\"\r\n      @close=\"showFinishPopup = false\"\r\n      :closeable=\"true\"\r\n    >\r\n      <view class=\"finish-popup\">\r\n        <view class=\"popup-title\">完成工单</view>\r\n        <view class=\"popup-content\">\r\n          <u-form\r\n            labelPosition=\"top\"\r\n            labelWidth=\"auto\"\r\n            :model=\"finishForm\"\r\n            ref=\"finishForm\"\r\n          >\r\n            <u-form-item\r\n              borderBottom\r\n              labelPosition=\"left\"\r\n              label=\"服务开始时间\"\r\n              required\r\n            >\r\n              <view\r\n                style=\"\r\n                  display: flex;\r\n                  justify-content: flex-end;\r\n                  align-items: center;\r\n                \"\r\n              >\r\n                <text @click=\"shiwServiceStartTimePicker = true\"\r\n                  >{{\r\n                    dateFormat(\r\n                      new Date(Number(finishForm.serviceStartTime)),\r\n                      \"yyyy-MM-dd hh:mm\"\r\n                    ) || \"请选择服务开始时间\"\r\n                  }}\r\n                  <u-icon label=\"uView\" size=\"40\" name=\"arrow-right\"></u-icon\r\n                ></text>\r\n              </view>\r\n              <u-datetime-picker\r\n                v-model=\"finishForm.serviceStartTime\"\r\n                :show=\"shiwServiceStartTimePicker\"\r\n                @cancel=\"shiwServiceStartTimePicker = false\"\r\n                @confirm=\"shiwServiceStartTimePicker = false\"\r\n                mode=\"datetime\"\r\n                :visibleItemCount=\"5\"\r\n              ></u-datetime-picker>\r\n            </u-form-item>\r\n            <u-form-item\r\n              borderBottom\r\n              labelPosition=\"left\"\r\n              label=\"服务结束时间\"\r\n              required\r\n            >\r\n              <view\r\n                style=\"\r\n                  display: flex;\r\n                  justify-content: flex-end;\r\n                  align-items: center;\r\n                \"\r\n              >\r\n                <text @click=\"shiwServiceEndTimePicker = true\"\r\n                  >{{\r\n                    dateFormat(\r\n                      new Date(Number(finishForm.serviceEndTime)),\r\n                      \"yyyy-MM-dd hh:mm\"\r\n                    ) || \"请选择服务结束时间\"\r\n                  }}\r\n                  <u-icon label=\"uView\" size=\"40\" name=\"arrow-right\"></u-icon\r\n                ></text>\r\n              </view>\r\n              <u-datetime-picker\r\n                v-model=\"finishForm.serviceEndTime\"\r\n                :show=\"shiwServiceEndTimePicker\"\r\n                @cancel=\"shiwServiceEndTimePicker = false\"\r\n                @confirm=\"shiwServiceEndTimePicker = false\"\r\n                mode=\"datetime\"\r\n                :visibleItemCount=\"5\"\r\n              ></u-datetime-picker>\r\n            </u-form-item>\r\n            <!-- 实际使用工时 -->\r\n            <u-form-item labelPosition=\"left\" label=\"实际使用工时\">\r\n              <u-input\r\n                v-model=\"finishForm.useTimes\"\r\n                placeholder=\"请输入实际使用工时\"\r\n                :border=\"false\"\r\n                type=\"digit\"\r\n              ></u-input>\r\n            </u-form-item>\r\n            <u-form-item labelPosition=\"left\" label=\"完成情况\">\r\n              <dicPicker\r\n                dicUrl=\"/blade-system/dict-biz/dictionary?code=completeType\"\r\n                v-model=\"finishForm.completeStatus\"\r\n                placeholder=\"请选择完成情况\"\r\n              ></dicPicker>\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"现场图\">\r\n              <uv-upload\r\n                accept=\"media\"\r\n                @clickPreview=\"handleClickPreview\"\r\n                :fileList=\"finishForm.workOrderPhotos\"\r\n                @afterRead=\"afterReadForXC\"\r\n                @delete=\"handleDeleteForXC\"\r\n                multiple\r\n                :maxCount=\"9\"\r\n              >\r\n              </uv-upload>\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"处理结果图\">\r\n              <uv-upload\r\n                accept=\"media\"\r\n                @clickPreview=\"handleClickPreview\"\r\n                :fileList=\"finishForm.handleResultPhotos\"\r\n                @afterRead=\"afterReadForFinish\"\r\n                @delete=\"handleDeleteForFinish\"\r\n                multiple\r\n                :maxCount=\"9\"\r\n              >\r\n              </uv-upload>\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"服务复盘\">\r\n              <u-textarea\r\n                v-model=\"finishForm.serviceReorder\"\r\n                border=\"none\"\r\n                placeholder=\"请输入服务复盘\"\r\n              ></u-textarea>\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"备注\">\r\n              <u-textarea\r\n                v-model=\"finishForm.completeRemark\"\r\n                border=\"none\"\r\n                placeholder=\"请输入备注信息\"\r\n              ></u-textarea>\r\n            </u-form-item>\r\n          </u-form>\r\n        </view>\r\n        <view class=\"popup-footer\">\r\n          <u-button type=\"primary\" @click=\"submitFinish\">提交</u-button>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport QQMapWX from \"@/utils/qqmap-wx-jssdk.min.js\";\r\nimport http from \"../../http/api.js\";\r\nimport { dateFormat } from \"../../utils/date.js\";\r\nimport dicPicker from \"@/components/dic-picker/dic-picker.vue\";\r\n\r\nexport default {\r\n  name: \"WokerOrderDetail\",\r\n  components: {\r\n    dicPicker,\r\n  },\r\n  data() {\r\n    return {\r\n      detailForm: {\r\n        // 添加模拟数据用于测试完工报告显示\r\n        objectStatus: 2, // 已完成状态\r\n        customerName: \"测试客户公司\",\r\n        contact: \"张经理\",\r\n        contactPhone: \"13800138000\",\r\n        distributionAddress: \"北京市朝阳区测试大厦10层\",\r\n        objectName: \"服务器维护任务\",\r\n        serverTypeName: \"硬件维护\",\r\n        serviceStartTime: \"2024-01-15 09:00:00\",\r\n        serviceEndTime: \"2024-01-15 18:00:00\",\r\n        projectLeaderName: \"项目经理\",\r\n        handleUserName: \"张工程师\",\r\n        SLATypeName: \"标准SLA\",\r\n        taskDescription: \"对服务器进行全面检查和维护，确保系统稳定运行。\",\r\n        fileList: [],\r\n        milestoneVOList: [\r\n          {\r\n            handleContent: \"任务已创建\",\r\n            createTime: \"2024-01-14 10:00:00\",\r\n          },\r\n          {\r\n            handleContent: \"工程师已接单\",\r\n            createTime: \"2024-01-14 14:30:00\",\r\n          },\r\n          {\r\n            handleContent: \"现场签到完成\",\r\n            createTime: \"2024-01-15 09:00:00\",\r\n          },\r\n          {\r\n            handleContent: \"任务已完成\",\r\n            createTime: \"2024-01-15 18:00:00\",\r\n          },\r\n        ],\r\n        sealContractObjectResultVOList: [\r\n          {\r\n            handleName: \"张工程师\",\r\n            serviceStartTime: \"2024-01-15 09:00:00\",\r\n            serviceEndTime: \"2024-01-15 17:30:00\",\r\n            useTimes: 8.5,\r\n            completeStatus: 1,\r\n            completeStatusName: \"完全完成\",\r\n            signTime: \"2024-01-15 08:55:00\",\r\n            signAddress: \"北京市朝阳区测试大厦10层\",\r\n            serviceReorder: \"本次服务顺利完成，客户设备运行正常。在服务过程中发现了一些潜在问题并及时处理，建议客户定期进行设备维护检查。\",\r\n            completeRemark: \"设备已恢复正常运行，客户满意度较高。所有问题已解决，系统运行稳定。\",\r\n            signPhotoList: [\r\n              { link: \"https://via.placeholder.com/300x200/673AB7/white?text=签到图1\" },\r\n              { link: \"https://via.placeholder.com/300x200/3F51B5/white?text=签到图2\" }\r\n            ],\r\n            workOrderPhotoList: [\r\n              { link: \"https://via.placeholder.com/300x200/4CAF50/white?text=现场图1\" },\r\n              { link: \"https://via.placeholder.com/300x200/2196F3/white?text=现场图2\" },\r\n              { link: \"https://via.placeholder.com/300x200/FF9800/white?text=现场图3\" }\r\n            ],\r\n            handleResultPhotoList: [\r\n              { link: \"https://via.placeholder.com/300x200/9C27B0/white?text=结果图1\" },\r\n              { link: \"https://via.placeholder.com/300x200/F44336/white?text=结果图2\" }\r\n            ]\r\n          },\r\n          {\r\n            handleName: \"李技术员\",\r\n            serviceStartTime: \"2024-01-16 14:00:00\",\r\n            serviceEndTime: \"2024-01-16 16:00:00\",\r\n            useTimes: 2,\r\n            completeStatus: 2,\r\n            completeStatusName: \"部分完成\",\r\n            signTime: \"2024-01-16 13:55:00\",\r\n            signAddress: \"北京市朝阳区测试大厦10层\",\r\n            serviceReorder: \"协助主工程师完成设备调试工作，负责数据备份和系统配置。\",\r\n            completeRemark: \"辅助工作完成良好，配合主工程师顺利完成任务。\",\r\n            signPhotoList: [\r\n              { link: \"https://via.placeholder.com/300x200/795548/white?text=李工签到\" }\r\n            ],\r\n            workOrderPhotoList: [\r\n              { link: \"https://via.placeholder.com/300x200/607D8B/white?text=辅助现场图\" }\r\n            ],\r\n            handleResultPhotoList: [\r\n              { link: \"https://via.placeholder.com/300x200/795548/white?text=辅助结果图\" }\r\n            ]\r\n          }\r\n        ]\r\n      },\r\n      dateFormat,\r\n      report: {\r\n        handleUserName: \"张工程师\",\r\n        serviceStartTime: \"2024-01-15 09:00:00\",\r\n        serviceEndTime: \"2024-01-15 17:30:00\",\r\n        useTimes: 8.5,\r\n        completeStatus: 1, // 完全完成\r\n        serviceReorder:\r\n          \"本次服务顺利完成，客户设备运行正常。在服务过程中发现了一些潜在问题并及时处理，建议客户定期进行设备维护检查。具体完成内容包括：\\n1. 服务器硬件检查\\n2. 系统性能优化\\n3. 安全补丁更新\\n4. 数据备份验证\",\r\n        completeRemark:\r\n          \"设备已恢复正常运行，客户满意度较高。所有问题已解决，系统运行稳定。\",\r\n        workOrderPhotos: [\r\n          {\r\n            url: \"https://via.placeholder.com/300x200/4CAF50/white?text=现场图1\",\r\n          },\r\n          {\r\n            url: \"https://via.placeholder.com/300x200/2196F3/white?text=现场图2\",\r\n          },\r\n          {\r\n            url: \"https://via.placeholder.com/300x200/FF9800/white?text=现场图3\",\r\n          },\r\n        ],\r\n        handleResultPhotos: [\r\n          {\r\n            url: \"https://via.placeholder.com/300x200/9C27B0/white?text=结果图1\",\r\n          },\r\n          {\r\n            url: \"https://via.placeholder.com/300x200/F44336/white?text=结果图2\",\r\n          },\r\n        ],\r\n      },\r\n\r\n      // 签到相关\r\n      showSignDrawer: false,\r\n      locationLoading: false,\r\n      signAddress: null,\r\n      signPhotoUrl: [],\r\n      signRemark: \"\",\r\n      qqmapsdk: null,\r\n\r\n      // 接单相关\r\n      acceptOrderModalShow: false,\r\n\r\n      // 完成相关\r\n      showFinishPopup: false,\r\n      shiwServiceStartTimePicker: false,\r\n      shiwServiceEndTimePicker: false,\r\n      finishForm: {\r\n        serviceStartTime: Number(new Date()),\r\n        serviceEndTime: Number(new Date()),\r\n        completeRemark: \"\",\r\n        useTimes: null,\r\n        serviceReorder: \"\",\r\n        workOrderPhotos: [],\r\n        handleResultPhotos: [],\r\n        completeStatus: null,\r\n      },\r\n\r\n      // 折叠面板状态\r\n      expandedSections: {},\r\n    };\r\n  },\r\n  computed: {\r\n    // 是否显示操作按钮\r\n    showActionButtons() {\r\n      if (!this.detailForm.objectStatus) return false;\r\n\r\n      // 显示条件：待接单 或 进行中\r\n      return (\r\n        this.detailForm.objectStatus == 3 || this.detailForm.objectStatus == 1\r\n      );\r\n    },\r\n    sealContractObjectResultVOList() {\r\n      return this.detailForm.sealContractObjectResultVOList || [];\r\n    },\r\n  },\r\n  onLoad({ id }) {\r\n    if (id) {\r\n      this.getWorkerOrderDetail(id);\r\n    }\r\n    // 初始化地图实例\r\n    this.qqmapsdk = new QQMapWX({\r\n      key: \"V37BZ-5JF63-HBZ3S-34XAJ-KPG2Q-74FPM\", // 请替换为你的真实 Key\r\n    });\r\n  },\r\n\r\n  methods: {\r\n    getWorkerOrderDetail(id) {\r\n      this.$u.api.getWorkerOrderDetail(id).then((res) => {\r\n        this.detailForm = res.data;\r\n        if (this.detailForm.completeFileList) {\r\n          this.detailForm.completeFileList =\r\n            this.detailForm.completeFileList.map((item) => {\r\n              return {\r\n                ...item,\r\n                url: item.link,\r\n              };\r\n            });\r\n        }\r\n\r\n        console.log(this.detailForm);\r\n      });\r\n    },\r\n\r\n    // 拨打电话\r\n    callPhone(phone) {\r\n      uni.makePhoneCall({\r\n        phoneNumber: phone,\r\n        success() {\r\n          console.log(\"拨打电话成功！\");\r\n        },\r\n        fail(err) {\r\n          console.log(\"拨打电话失败！\", err);\r\n        },\r\n      });\r\n    },\r\n\r\n    // 接单\r\n    handleAccept() {\r\n      this.acceptOrderModalShow = true;\r\n    },\r\n\r\n    acceptOrderConfirm() {\r\n      this.$u.api\r\n        .startWorkerOrder(this.detailForm.id)\r\n        .then((res) => {\r\n          this.acceptOrderModalShow = false;\r\n          this.$u.toast(\"接单成功\");\r\n          // 重新获取详情\r\n          this.getWorkerOrderDetail(this.detailForm.id);\r\n        })\r\n        .catch((err) => {\r\n          this.$u.toast(err.message || \"接单失败\");\r\n        });\r\n    },\r\n\r\n    // 签到\r\n    handleSign() {\r\n      this.showSignDrawer = true;\r\n      this.signAddress = null;\r\n      this.signPhotoUrl = [];\r\n      this.signRemark = \"\";\r\n      this.chooseLocation();\r\n    },\r\n\r\n    chooseLocation() {\r\n      this.locationLoading = true;\r\n      wx.getLocation({\r\n        type: \"gcj02\",\r\n        success: (res) => {\r\n          console.log(res);\r\n          // 成功获取经纬度后，进行逆地址解析\r\n          this.qqmapsdk.reverseGeocoder({\r\n            location: {\r\n              latitude: res.latitude,\r\n              longitude: res.longitude,\r\n            },\r\n            success: (result) => {\r\n              // 逆解析成功回调\r\n              console.log(\"逆地址解析结果：\", result);\r\n              const addressInfo = result.result.address_component;\r\n              const formattedAddress = result.result.address;\r\n              console.log(\"所在城市：\", addressInfo.city);\r\n              console.log(\"完整地址：\", formattedAddress);\r\n              this.locationLoading = false;\r\n              this.signAddress = formattedAddress;\r\n            },\r\n            fail: (err) => {\r\n              this.locationLoading = false;\r\n              console.error(\"逆地址解析失败：\", err);\r\n              uni.showToast({ title: \"位置解析失败\", icon: \"none\" });\r\n            },\r\n          });\r\n        },\r\n        fail: (err) => {\r\n          console.log(err);\r\n          this.locationLoading = false;\r\n          uni.showToast({ title: \"位置选择失败\", icon: \"none\" });\r\n        },\r\n      });\r\n    },\r\n\r\n    afterReadSign(event) {\r\n      console.log(event);\r\n      const { file } = event;\r\n      const indexAll = this.signPhotoUrl.length;\r\n      file.forEach((item, index) => {\r\n        this.signPhotoUrl.push({\r\n          ...item,\r\n          status: \"uploading\",\r\n          message: \"上传中\",\r\n          index: indexAll + index,\r\n        });\r\n      });\r\n      file.forEach((item, index) => {\r\n        this.uploadFileSign(item.url, indexAll + index);\r\n      });\r\n    },\r\n\r\n    uploadFileSign(url, index) {\r\n      return new Promise((resolve) => {\r\n        const params = {\r\n          filePath: url,\r\n          name: \"file\",\r\n        };\r\n        http.upload(\"/blade-resource/attach/upload\", params).then((res) => {\r\n          this.signPhotoUrl.find((item) => item.index == index).status =\r\n            \"success\";\r\n          this.signPhotoUrl.find((item) => item.index == index).message = \"\";\r\n          this.signPhotoUrl.find((item) => item.index == index).url =\r\n            res.data.link;\r\n          resolve();\r\n        });\r\n      });\r\n    },\r\n\r\n    handleDeleteSign({ file, index, name }) {\r\n      console.log(file, index, name);\r\n      this.signPhotoUrl.splice(index, 1);\r\n    },\r\n\r\n    handleClickPreview(url, lists, name) {\r\n      console.log(url, lists, name);\r\n    },\r\n\r\n    submitSign() {\r\n      if (!this.signPhotoUrl || this.signPhotoUrl.length === 0) {\r\n        uni.showToast({ title: \"请上传照片\", icon: \"none\" });\r\n        return;\r\n      }\r\n      // 提交签到数据\r\n      const data = {\r\n        id: this.detailForm.id,\r\n        address: this.signAddress,\r\n        signPhotoUrl: this.signPhotoUrl.map((item) => item.url).join(\",\"),\r\n      };\r\n      this.$u.api\r\n        .signIn(data)\r\n        .then((res) => {\r\n          console.log(res);\r\n          uni.showToast({ title: \"签到成功\", icon: \"success\" });\r\n          this.showSignDrawer = false;\r\n          // 重新获取详情\r\n          this.getWorkerOrderDetail(this.detailForm.id);\r\n        })\r\n        .catch((err) => {\r\n          this.$u.toast(err.message || \"签到失败\");\r\n        });\r\n    },\r\n\r\n    // 完成任务\r\n    handleFinish() {\r\n      this.showFinishPopup = true;\r\n      this.finishForm.serviceStartTime = Number(\r\n        new Date(this.detailForm.serviceStartTime)\r\n      );\r\n      this.finishForm.serviceEndTime = Number(\r\n        new Date(this.detailForm.serviceEndTime)\r\n      );\r\n    },\r\n\r\n    afterReadForXC(event) {\r\n      console.log(event);\r\n      const { file } = event;\r\n      const indexAll = this.finishForm.workOrderPhotos.length;\r\n      file.forEach((item, index) => {\r\n        this.finishForm.workOrderPhotos.push({\r\n          ...item,\r\n          status: \"uploading\",\r\n          message: \"上传中\",\r\n          index: indexAll + index,\r\n        });\r\n      });\r\n      file.forEach((item, index) => {\r\n        this.uploadFileForXC(item.url, indexAll + index);\r\n      });\r\n    },\r\n\r\n    uploadFileForXC(url, index) {\r\n      return new Promise((resolve) => {\r\n        const params = {\r\n          filePath: url,\r\n          name: \"file\",\r\n        };\r\n        http.upload(\"/blade-resource/attach/upload\", params).then((res) => {\r\n          this.finishForm.workOrderPhotos.find(\r\n            (item) => item.index == index\r\n          ).status = \"success\";\r\n          this.finishForm.workOrderPhotos.find(\r\n            (item) => item.index == index\r\n          ).message = \"\";\r\n          this.finishForm.workOrderPhotos.find(\r\n            (item) => item.index == index\r\n          ).url = res.data.link;\r\n          this.finishForm.workOrderPhotos.find(\r\n            (item) => item.index == index\r\n          ).id = res.data.id;\r\n          resolve();\r\n        });\r\n      });\r\n    },\r\n\r\n    handleDeleteForXC({ file, index, name }) {\r\n      console.log(file, index, name);\r\n      this.finishForm.workOrderPhotos.splice(index, 1);\r\n    },\r\n\r\n    afterReadForFinish(event) {\r\n      console.log(event);\r\n      const { file } = event;\r\n      const indexAll = this.finishForm.handleResultPhotos.length;\r\n      file.forEach((item, index) => {\r\n        this.finishForm.handleResultPhotos.push({\r\n          ...item,\r\n          status: \"uploading\",\r\n          message: \"上传中\",\r\n          index: indexAll + index,\r\n        });\r\n      });\r\n      file.forEach((item, index) => {\r\n        this.uploadFileForFinish(item.url, indexAll + index);\r\n      });\r\n    },\r\n\r\n    uploadFileForFinish(url, index) {\r\n      return new Promise((resolve) => {\r\n        const params = {\r\n          filePath: url,\r\n          name: \"file\",\r\n        };\r\n        http.upload(\"/blade-resource/attach/upload\", params).then((res) => {\r\n          this.finishForm.handleResultPhotos.find(\r\n            (item) => item.index == index\r\n          ).status = \"success\";\r\n          this.finishForm.handleResultPhotos.find(\r\n            (item) => item.index == index\r\n          ).message = \"\";\r\n          this.finishForm.handleResultPhotos.find(\r\n            (item) => item.index == index\r\n          ).url = res.data.link;\r\n          this.finishForm.handleResultPhotos.find(\r\n            (item) => item.index == index\r\n          ).id = res.data.id;\r\n          resolve();\r\n        });\r\n      });\r\n    },\r\n\r\n    handleDeleteForFinish({ file, index, name }) {\r\n      console.log(file, index, name);\r\n      this.finishForm.handleResultPhotos.splice(index, 1);\r\n    },\r\n\r\n    submitFinish() {\r\n      if (\r\n        !this.finishForm.serviceStartTime ||\r\n        !this.finishForm.serviceEndTime\r\n      ) {\r\n        this.$u.toast(\"开始时间和结束时间不能为空\");\r\n        return;\r\n      }\r\n      const formData = {\r\n        id: this.detailForm.id,\r\n        serviceStartTime: this.dateFormat(\r\n          new Date(Number(this.finishForm.serviceStartTime)),\r\n          \"yyyy-MM-dd hh:mm:ss\"\r\n        ),\r\n        serviceEndTime: this.dateFormat(\r\n          new Date(Number(this.finishForm.serviceEndTime)),\r\n          \"yyyy-MM-dd hh:mm:ss\"\r\n        ),\r\n        completeRemark: this.finishForm.completeRemark,\r\n        handleResultPhotos:\r\n          this.finishForm.handleResultPhotos &&\r\n          this.finishForm.handleResultPhotos.map((item) => item.id).join(\",\"),\r\n        workOrderPhotos:\r\n          this.finishForm.workOrderPhotos &&\r\n          this.finishForm.workOrderPhotos.map((item) => item.id).join(\",\"),\r\n        useTimes: this.finishForm.useTimes,\r\n        completeStatus: this.finishForm.completeStatus,\r\n        serviceReorder: this.finishForm.serviceReorder,\r\n      };\r\n      this.$u.api\r\n        .finishWorkerOrder(formData)\r\n        .then((res) => {\r\n          this.$u.toast(\"提交成功\");\r\n          this.showFinishPopup = false;\r\n          this.finishForm = {\r\n            serviceStartTime: Number(new Date()),\r\n            serviceEndTime: Number(new Date()),\r\n            completeRemark: \"\",\r\n            useTimes: null,\r\n            serviceReorder: \"\",\r\n            workOrderPhotos: [],\r\n            handleResultPhotos: [],\r\n            completeStatus: null,\r\n          };\r\n          // 重新获取详情\r\n          this.getWorkerOrderDetail(this.detailForm.id);\r\n        })\r\n        .catch((err) => {\r\n          this.$u.toast(err.message || \"提交失败\");\r\n        });\r\n    },\r\n\r\n    // 获取完成状态文本\r\n    getCompleteStatusText(status) {\r\n      const statusMap = {\r\n        1: \"完全完成\",\r\n        2: \"部分完成\",\r\n        3: \"未完成\",\r\n      };\r\n      return statusMap[status] || \"未知状态\";\r\n    },\r\n\r\n    // 获取完成状态样式类\r\n    getCompleteStatusClass(status) {\r\n      const classMap = {\r\n        1: \"status-complete\",\r\n        2: \"status-partial\",\r\n        3: \"status-incomplete\",\r\n      };\r\n      return classMap[status] || \"\";\r\n    },\r\n\r\n    // 预览图片\r\n    previewImage(current, urls) {\r\n      const imageUrls = urls.map((item) => item.link || item.url);\r\n      uni.previewImage({\r\n        current: current,\r\n        urls: imageUrls,\r\n      });\r\n    },\r\n\r\n    // 切换折叠面板状态\r\n    toggleSection(type, index) {\r\n      const key = `${type}_${index}`;\r\n      this.$set(this.expandedSections, key, !this.expandedSections[key]);\r\n    },\r\n\r\n    // 获取折叠面板展开状态\r\n    getExpandedState(type, index) {\r\n      const key = `${type}_${index}`;\r\n      // 默认展开完成信息，签到信息默认收起\r\n      if (this.expandedSections[key] === undefined) {\r\n        return type === 'complete';\r\n      }\r\n      return this.expandedSections[key];\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.order-detail {\r\n  padding: 24rpx;\r\n  background: linear-gradient(180deg, #f8faff 0%, #f0f4ff 100%);\r\n  min-height: 100vh;\r\n}\r\n\r\n.card {\r\n  background: white;\r\n  border-radius: 20rpx;\r\n  margin-bottom: 24rpx;\r\n  overflow: hidden;\r\n  border: 1rpx solid #e8f0fe;\r\n  box-shadow: 0 8rpx 24rpx rgba(41, 121, 255, 0.08);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.shadow:hover {\r\n  box-shadow: 0 12rpx 32rpx rgba(41, 121, 255, 0.12);\r\n  transform: translateY(-2rpx);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 28rpx 24rpx;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #fff 100%);\r\n  position: relative;\r\n}\r\n\r\n.card-header::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4rpx;\r\n  background: linear-gradient(\r\n    90deg,\r\n    rgba(255, 255, 255, 0.7) 0%,\r\n    rgba(255, 255, 255, 0.1) 100%\r\n  );\r\n}\r\n\r\n.header-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.card-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #000;\r\n  letter-spacing: 0.5rpx;\r\n}\r\n\r\n.card-content {\r\n  padding: 32rpx 24rpx;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  margin-bottom: 24rpx;\r\n  gap: 24rpx;\r\n}\r\n\r\n.info-row.single {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.info-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.info-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8rpx;\r\n}\r\n\r\n.info-item.full {\r\n  flex: 1;\r\n}\r\n\r\n.label {\r\n  font-size: 24rpx;\r\n  color: #8a8a8a;\r\n  font-weight: 500;\r\n  letter-spacing: 0.5rpx;\r\n}\r\n\r\n.value {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  font-weight: 500;\r\n  line-height: 1.4;\r\n}\r\n\r\n.value.important {\r\n  color: #2979ff;\r\n  font-weight: 600;\r\n}\r\n\r\n.value.tag {\r\n  background: #e3f2fd;\r\n  color: #1976d2;\r\n  padding: 6rpx 12rpx;\r\n  border-radius: 16rpx;\r\n  font-size: 24rpx;\r\n  align-self: flex-start;\r\n}\r\n\r\n.value.time {\r\n  color: #ff6b35;\r\n  font-weight: 500;\r\n}\r\n\r\n.value.phone {\r\n  color: #2979ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.value.satisfy {\r\n  color: #4caf50;\r\n  font-weight: 600;\r\n  background: #e8f5e8;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 12rpx;\r\n  align-self: flex-start;\r\n}\r\n\r\n.value.code {\r\n  font-family: \"Courier New\", monospace;\r\n  background: #f5f5f5;\r\n  padding: 8rpx 12rpx;\r\n  border-radius: 8rpx;\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.value.desc {\r\n  background: #f8f9fa;\r\n  padding: 16rpx;\r\n  border-radius: 12rpx;\r\n  border-left: 4rpx solid #2979ff;\r\n}\r\n\r\n.value.file-link {\r\n  color: #2979ff;\r\n  text-decoration: underline;\r\n  background: #e3f2fd;\r\n  padding: 8rpx 12rpx;\r\n  border-radius: 8rpx;\r\n  align-self: flex-start;\r\n}\r\n\r\n.timeline-container {\r\n  padding: 16rpx;\r\n}\r\n\r\n.timeline {\r\n  position: relative;\r\n  padding-left: 48rpx;\r\n}\r\n\r\n.timeline-item {\r\n  position: relative;\r\n  padding-bottom: 32rpx;\r\n}\r\n\r\n.timeline-item:last-child {\r\n  padding-bottom: 0;\r\n}\r\n\r\n.timeline-dot {\r\n  position: absolute;\r\n  left: -54rpx;\r\n  top: 12rpx;\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n  background: #e0e0e0;\r\n  border-radius: 50%;\r\n  border: 4rpx solid white;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n  z-index: 2;\r\n}\r\n\r\n.timeline-dot.active {\r\n  background: #2979ff;\r\n  box-shadow: 0 0 0 4rpx rgba(41, 121, 255, 0.2);\r\n}\r\n\r\n.timeline-line {\r\n  position: absolute;\r\n  left: -46rpx;\r\n  top: 36rpx;\r\n  bottom: -32rpx;\r\n  width: 4rpx;\r\n  background: linear-gradient(180deg, #2979ff 0%, #e0e0e0 100%);\r\n  border-radius: 2rpx;\r\n}\r\n\r\n.timeline-item:last-child .timeline-line {\r\n  display: none;\r\n}\r\n\r\n.timeline-content {\r\n  background: white;\r\n  padding: 24rpx;\r\n  border-radius: 16rpx;\r\n  border: 1rpx solid #e8f0fe;\r\n  box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.05);\r\n  position: relative;\r\n}\r\n\r\n.timeline-content::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: -12rpx;\r\n  top: 20rpx;\r\n  width: 0;\r\n  height: 0;\r\n  border-top: 8rpx solid transparent;\r\n  border-bottom: 8rpx solid transparent;\r\n  border-right: 12rpx solid white;\r\n}\r\n\r\n.timeline-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.timeline-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #2979ff;\r\n}\r\n\r\n.timeline-time {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  background: #f5f5f5;\r\n  padding: 4rpx 8rpx;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.timeline-desc {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 8rpx;\r\n  line-height: 1.4;\r\n}\r\n\r\n.timeline-operator {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  font-style: italic;\r\n}\r\n\r\n/* 底部占位空间 */\r\n.bottom-placeholder {\r\n  height: 240rpx; /* 为固定底部操作栏留出空间 */\r\n}\r\n\r\n/* 固定底部操作栏 */\r\n.fixed-action-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: #fff;\r\n  padding: 24rpx 32rpx;\r\n  padding-bottom: calc(24rpx + env(safe-area-inset-bottom)); /* 适配安全区域 */\r\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  border-top: 1rpx solid #f0f0f0;\r\n  z-index: 999;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 24rpx;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n}\r\n\r\n.action-btn {\r\n  min-width: 120rpx;\r\n  padding: 0 32rpx;\r\n  height: 72rpx;\r\n  line-height: 72rpx;\r\n  border: none;\r\n  border-radius: 36rpx;\r\n  background: #f5f7fa;\r\n  color: #2979ff;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  margin: 0;\r\n  outline: none;\r\n  box-shadow: 0 4rpx 12rpx 0 rgba(45, 140, 240, 0.15);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn.primary {\r\n  background: linear-gradient(90deg, #2979ff 0%, #57a3f3 100%);\r\n  color: #fff;\r\n  box-shadow: 0 4rpx 16rpx 0 rgba(45, 140, 240, 0.3);\r\n}\r\n\r\n.action-btn:active {\r\n  opacity: 0.85;\r\n  transform: translateY(2rpx);\r\n}\r\n\r\n/* 完成弹窗样式 */\r\n.finish-popup {\r\n  height: 80vh;\r\n  overflow-y: auto;\r\n  position: relative;\r\n}\r\n\r\n.popup-title {\r\n  padding-top: 20rpx;\r\n  position: sticky;\r\n  background-color: #fff;\r\n  top: 0;\r\n  z-index: 10;\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  text-align: center;\r\n}\r\n\r\n.popup-content {\r\n  padding: 30rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.popup-footer {\r\n  padding: 20rpx 0;\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 20rpx;\r\n  right: 20rpx;\r\n  width: auto;\r\n}\r\n\r\n/* 输入框右对齐 */\r\n::v-deep input {\r\n  text-align: right !important;\r\n}\r\n\r\n/* 完工报告样式 */\r\n.completion-report {\r\n  position: relative;\r\n  margin-bottom: 32rpx;\r\n}\r\n\r\n.report-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n  padding: 16rpx 20rpx;\r\n  background: linear-gradient(135deg, #f8faff 0%, #e3f2fd 100%);\r\n  border-radius: 12rpx;\r\n  border-left: 4rpx solid #2979ff;\r\n}\r\n\r\n.report-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #2979ff;\r\n}\r\n\r\n.report-time {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  background: rgba(255, 255, 255, 0.8);\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 12rpx;\r\n}\r\n\r\n/* 折叠面板样式 */\r\n.collapsible-section {\r\n  margin-bottom: 16rpx;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  border: 1rpx solid #e8f0fe;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16rpx 20rpx;\r\n  background: #f8faff;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.section-header:active {\r\n  background: #e3f2fd;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n}\r\n\r\n.section-text {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.section-arrow {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.section-arrow.expanded {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.section-content {\r\n  padding: 20rpx;\r\n  background: #fff;\r\n  border-top: 1rpx solid #e8f0fe;\r\n}\r\n\r\n.report-divider {\r\n  height: 1rpx;\r\n  background: linear-gradient(\r\n    90deg,\r\n    transparent 0%,\r\n    #e0e0e0 50%,\r\n    transparent 100%\r\n  );\r\n  margin: 32rpx 0;\r\n}\r\n\r\n/* 完成状态标签样式 */\r\n.value.tag.status-complete {\r\n  background: #e8f5e8;\r\n  color: #4caf50;\r\n}\r\n\r\n.value.tag.status-partial {\r\n  background: #fff3e0;\r\n  color: #ff9800;\r\n}\r\n\r\n.value.tag.status-incomplete {\r\n  background: #ffebee;\r\n  color: #f44336;\r\n}\r\n\r\n/* 图片网格样式 */\r\n.photo-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12rpx;\r\n  margin-top: 12rpx;\r\n}\r\n\r\n.photo-item {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  border: 1rpx solid #e0e0e0;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.photo-item:active {\r\n  transform: scale(0.95);\r\n  opacity: 0.8;\r\n}\r\n\r\n.photo-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrderDetail.vue?vue&type=style&index=0&id=41762ff7&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wokerOrderDetail.vue?vue&type=style&index=0&id=41762ff7&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759051007735\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}