{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/project/vt-unih5-order/pages/index/index.vue?ab7c", "webpack:///D:/project/vt-unih5-order/pages/index/index.vue?2bd2", "webpack:///D:/project/vt-unih5-order/pages/index/index.vue?1209", "uni-app:///pages/index/index.vue", "webpack:///D:/project/vt-unih5-order/pages/index/index.vue?8db1", "webpack:///D:/project/vt-unih5-order/pages/index/index.vue?a3ae"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "showPC", "dateFormat", "summary", "totalNum", "wait<PERSON>um", "do<PERSON>um", "completeNum", "tabs", "label", "value", "currentTab", "orders", "statusMap", "showSignDrawer", "locationLoading", "signAddress", "signPhotoUrl", "signRemark", "signOrder", "qqmapsdk", "currentItem", "acceptOrderModalShow", "showFinishPopup", "shiwServiceStartTimePicker", "shiwServiceEndTimePicker", "finishForm", "serviceStartTime", "serviceEndTime", "completeRemark", "useTimes", "serviceReorder", "workOrderPhotos", "handleResultPhotos", "components", "dicPicker", "computed", "filteredOrders", "onLoad", "key", "onReachBottom", "console", "onPullDownRefresh", "uni", "methods", "wxlogin", "complete", "wxToken", "code", "then", "resolve", "catch", "title", "handleClickTab", "fetchOrders", "map", "getWorkerOrder", "pageNum", "pageSize", "objectStatus", "getTaskStatistics", "handleAccept", "acceptOrderConfirm", "handleSign", "chooseLocation", "type", "success", "location", "latitude", "longitude", "fail", "icon", "afterReadSign", "file", "item", "status", "message", "index", "uploadFileSign", "filePath", "name", "http", "res", "handleDeleteSign", "handleClickPreview", "submitSign", "id", "address", "signIn", "handleFinish", "afterReadForXC", "uploadFileForXC", "handleDeleteForXC", "afterReadFor<PERSON>inish", "uploadFileForFinish", "handleDeleteForFinish", "submit<PERSON><PERSON>sh", "completeStatus", "finishWorkerOrder", "finishTime", "toDetail", "url", "makePhoneCall", "phoneNumber"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpIA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC2WtnB;AACA;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC,OACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;QACA;QACA;QACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;MACA;QAAA;MAAA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cACA;cACA;cACA;cACA;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACAC;IAEA;EACA;EACAC;IACA;IACA;IACAC;EACA;EACAC;IACAC;MAAA;MACA;QACAF;UACAG;YACA,cACAC;cAAAC;YAAA,GACAC;cACA;cACAC;YACA,GACAC;cACA;gBAAAC;cAAA;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACA;kBACA;gBACA;gBACA,cACAC;kBACAC;kBACAC;kBACAC;gBACA,GACAV;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAW;MAAA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACArE;QACAsE;QAEAC;UACAzB;;UAEA;UACA;YACA0B;cACAC;cACAC;YACA;YACAH;cACA;cACAzB;cACA;cACA;cACA;cACAA;cACAA;cACA;cACA;cACA;YACA;;YACA6B;cACA;cACA7B;cACAE;gBAAAS;gBAAAmB;cAAA;YACA;UACA;QACA;QACAD;UACA7B;UACA;UACAE;YAAAS;YAAAmB;UAAA;QACA;MACA;IACA;IACAC;MAAA;MACA/B;MACA;MACA;MACAgC;QACA,yDACAC;UACAC;UACAC;UACA;UACAC;QAAA,GACA;MACA;MACAJ;QACA;MACA;IACA;IACAK;MAAA;MACA;QACA;UACAC;UACAC;QACA;QACAC;UACA;YAAA;UAAA;UACA;YAAA;UAAA;UACA;YAAA;UAAA,SACAC;QACA;MACA;IACA;IACAC;MAAA;QAAAN;QAAAG;MACAvC;MACA;IACA;IACA2C;MACA3C;IACA;IACA4C;MAAA;MACA;MACA;MACA;MACA;MACA;QACA1C;UAAAS;UAAAmB;QAAA;QACA;MACA;MACA;MACA;QACAe;QACAC;QACAtE;UAAA;QAAA;QACA;MACA;;MACA,YACAuE,aACAvC;QACAR;MACA,GACAQ;QACAN;UAAAS;UAAAmB;QAAA;QACA;QACA;MACA;IACA;IAEAkB;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAjD;MACA;MACA;MACAgC;QACA,wEACAC;UACAC;UACAC;UACA;UACAC;QAAA,GACA;MACA;MACAJ;QACA;MACA;IACA;IACAkB;MAAA;MACA;QACA;UACAZ;UACAC;QACA;QACAC;UACA,wCACA;YAAA;UAAA,EACA;UACA,wCACA;YAAA;UAAA,EACA;UACA,wCACA;YAAA;UAAA,EACA;UACA,wCACA;YAAA;UAAA,EACA;QACA;MACA;IACA;IACAW;MAAA;QAAAf;QAAAG;MACAvC;MACA;IACA;IAEAoD;MAAA;MACApD;MACA;MACA;MACAgC;QACA,2EACAC;UACAC;UACAC;UACA;UACAC;QAAA,GACA;MACA;MACAJ;QACA;MACA;IACA;IACAqB;MAAA;MACA;QACA;UACAf;UACAC;QACA;QACAC;UACA,2CACA;YAAA;UAAA,EACA;UACA,2CACA;YAAA;UAAA,EACA;UACA,2CACA;YAAA;UAAA,EACA;UACA,2CACA;YAAA;UAAA,EACA;QACA;MACA;IACA;IACAc;MAAA;QAAAlB;QAAAG;MACAvC;MACA;IACA;IAEAuD;MAAA;MACA;QACA;QACA;MACA;MACA;QACAV;QACA3D,kCACA,oDACA,sBACA;QACAC,gCACA,kDACA,sBACA;QACAC;QACA;QACA;QACA;QACAI,oBACA,sCACA;UAAA;QAAA;QACAD,iBACA,mCACA;UAAA;QAAA;QACAF;QACAmE;QACAlE;MACA;MACA,YACAmE,4BACAjD;QACA;QACA;QACA;UACAkD;UACAtE;UACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA,GACAkB;QACA;MACA;IACA;IACAiD;MACAzD;QACA0D;MACA;IACA;IACAC;MACA3D;QACA4D;QACArC;UACAzB;QACA;QACA6B;UACA7B;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjxBA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uvUpload: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-upload/components/uv-upload/uv-upload\" */ \"@/uni_modules/uv-upload/components/uv-upload/uv-upload.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-modal/u-modal\" */ \"@/uni_modules/uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker\" */ \"@/uni_modules/uview-ui/components/u-datetime-picker/u-datetime-picker.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uTextarea: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-textarea/u-textarea\" */ \"@/uni_modules/uview-ui/components/u-textarea/u-textarea.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.orders.length\n  var m0 =\n    _vm.dateFormat(\n      new Date(Number(_vm.finishForm.serviceStartTime)),\n      \"yyyy-MM-dd hh:mm\"\n    ) || \"青选择服务开始时间\"\n  var m1 =\n    _vm.dateFormat(\n      new Date(Number(_vm.finishForm.serviceEndTime)),\n      \"yyyy-MM-dd hh:mm\"\n    ) || \"青选择服务结束时间\"\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showFinishPopup = false\n      _vm.showSignDrawer = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showSignDrawer = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.acceptOrderModalShow = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.showFinishPopup = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.shiwServiceStartTimePicker = true\n    }\n    _vm.e5 = function ($event) {\n      _vm.shiwServiceStartTimePicker = false\n    }\n    _vm.e6 = function ($event) {\n      _vm.shiwServiceStartTimePicker = false\n    }\n    _vm.e7 = function ($event) {\n      _vm.shiwServiceEndTimePicker = true\n    }\n    _vm.e8 = function ($event) {\n      _vm.shiwServiceEndTimePicker = false\n    }\n    _vm.e9 = function ($event) {\n      _vm.shiwServiceEndTimePicker = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <page-meta\r\n    :page-style=\"\r\n      'overflow:' + (showFinishPopup || showSignDrawer ? 'hidden' : 'visible')\r\n    \"\r\n  ></page-meta>\r\n  <page-container\r\n    :show=\"showFinishPopup || showSignDrawer\"\r\n    @beforeleave=\"\r\n      showFinishPopup = false;\r\n      showSignDrawer = false;\r\n    \"\r\n  ></page-container>\r\n  <view class=\"workbench-wrap\">\r\n    <!-- 工单概况统计卡片 -->\r\n    <view class=\"summary-card\">\r\n      <view class=\"summary-title\">工单概况</view>\r\n      <view class=\"summary-stats\">\r\n        <view class=\"stat-item total\">\r\n          <view class=\"stat-label\">工单总数</view>\r\n          <view class=\"stat-value\">{{ summary.totalNum }}</view>\r\n        </view>\r\n        <view class=\"stat-item pending\">\r\n          <view class=\"stat-label\">待接单</view>\r\n          <view class=\"stat-value\">{{ summary.waitNum }}</view>\r\n        </view>\r\n        <view class=\"stat-item processing\">\r\n          <view class=\"stat-label\">进行中</view>\r\n          <view class=\"stat-value\">{{ summary.doNum }}</view>\r\n        </view>\r\n        <view class=\"stat-item finished\">\r\n          <view class=\"stat-label\">已完成</view>\r\n          <view class=\"stat-value\">{{ summary.completeNum }}</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 工单tab栏 -->\r\n    <view class=\"order-tab-bar\">\r\n      <view\r\n        v-for=\"(tab, idx) in tabs\"\r\n        :key=\"tab.value\"\r\n        :class=\"['tab-item', { active: currentTab === idx }]\"\r\n        @tap=\"handleClickTab(idx)\"\r\n      >\r\n        {{ tab.label }}\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 工单列表 -->\r\n    <view class=\"order-list\">\r\n      <view v-if=\"orders.length === 0\" class=\"empty-tip\"> 暂无工单 </view>\r\n      <view\r\n        v-for=\"order in orders\"\r\n        @click.stop=\"toDetail(order)\"\r\n        :key=\"order.id\"\r\n        class=\"order-card\"\r\n      >\r\n        <view class=\"order-header\">\r\n          <view class=\"order-title\">{{ order.objectName }}</view>\r\n          <view style=\"display: flex; gap: 8rpx\">\r\n            <view\r\n              class=\"order-status\"\r\n              :class=\"'status-' + order.objectStatus\"\r\n              >{{ statusMap[order.objectStatus] }}</view\r\n            >\r\n            <view\r\n              v-if=\"order.isNeedSign == 1 && order.isSign == 0\"\r\n              class=\"order-status status-needSign\"\r\n            >\r\n              待签到\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">服务类型：</view>\r\n          <view class=\"order-value\">{{ order.serverTypeName }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">服务时间：</view>\r\n          <view class=\"order-value\"\r\n            >{{ order.serviceStartTime }} - {{ order.serviceEndTime }}</view\r\n          >\r\n        </view>\r\n        <!-- <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">工单描述：</view>\r\n          <view class=\"order-value\">{{ order.taskDescription }}</view>\r\n        </view> -->\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">客户名称：</view>\r\n          <view class=\"order-value\">{{ order.customerName }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">联系人：</view>\r\n          <view class=\"order-value\">{{ order.contact }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">联系电话：</view>\r\n          <view class=\"order-value\">{{ order.contactPhone }}</view><u-icon \r\n              v-if=\"order.contactPhone\" \r\n              name=\"phone\" \r\n              size=\"20\" \r\n              color=\"#2979ff\" \r\n              @tap.stop=\"makePhoneCall(order.contactPhone)\"\r\n              style=\"margin-left: 10rpx; cursor: pointer;\"\r\n            ></u-icon>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">地址：</view>\r\n          <view class=\"order-value\">{{ order.distributionAddress }}</view>\r\n        </view>\r\n        <!-- 操作区 -->\r\n        <view class=\"order-actions\">\r\n          <template v-if=\"order.objectStatus == 3\">\r\n            <u-button\r\n              type=\"primary\"\r\n              plain\r\n              class=\"action-btn primary\"\r\n              @tap.stop=\"handleAccept(order)\"\r\n              >接单</u-button\r\n            >\r\n          </template>\r\n          <template>\r\n            <u-button\r\n              class=\"action-btn\"\r\n              type=\"primary\"\r\n              v-if=\"\r\n                order.objectStatus == 1 &&\r\n                order.isNeedSign == 1 &&\r\n                order.isSign == 0\r\n              \"\r\n              icon=\"map\"\r\n              plain\r\n              hairline\r\n              @tap.stop=\"handleSign(order)\"\r\n              >签到</u-button\r\n            >\r\n\r\n            <u-button\r\n              class=\"action-btn primary\"\r\n              plain\r\n              type=\"primary\"\r\n              v-if=\"\r\n                order.isNeedSign == 1\r\n                  ? order.isSign == 1\r\n                  : order.objectStatus == 1\r\n              \"\r\n              @tap.stop=\"handleFinish(order)\"\r\n              >完成</u-button\r\n            >\r\n          </template>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <!-- 签到抽屉 -->\r\n    <u-popup\r\n      :show=\"showSignDrawer\"\r\n      type=\"bottom\"\r\n      closeOnClickOverlay\r\n      @close=\"showSignDrawer = false\"\r\n      :mask-click=\"true\"\r\n      background=\"#fff\"\r\n      style=\"z-index: 9999\"\r\n    >\r\n      <view style=\"padding: 32rpx 24rpx\">\r\n        <view style=\"font-size: 32rpx; font-weight: bold; margin-bottom: 24rpx\"\r\n          >签到</view\r\n        >\r\n        <view style=\"margin-bottom: 24rpx\">\r\n          <view style=\"font-size: 28rpx; margin-bottom: 8rpx\">选择位置</view>\r\n          <u-button\r\n            @tap=\"chooseLocation\"\r\n            type=\"primary\"\r\n            icon=\"map\"\r\n            :loading=\"locationLoading\"\r\n            loadingText=\"正在获取位置...\"\r\n            plain\r\n          >\r\n            {{ signAddress ? signAddress : \"点击获取位置\" }}\r\n          </u-button>\r\n        </view>\r\n        <view style=\"margin-bottom: 24rpx\">\r\n          <view style=\"font-size: 28rpx; margin-bottom: 8rpx\">上传照片</view>\r\n          <uv-upload\r\n            accept=\"media\"\r\n            @clickPreview=\"handleClickPreview\"\r\n            :fileList=\"signPhotoUrl\"\r\n            @afterRead=\"afterReadSign\"\r\n            @delete=\"handleDeleteSign\"\r\n            multiple\r\n            :maxCount=\"9\"\r\n          >\r\n          </uv-upload>\r\n        </view>\r\n        <!-- <view style=\"margin-bottom: 24rpx\">\r\n          <view style=\"font-size: 28rpx; margin-bottom: 8rpx\">备注</view>\r\n          <u-input\r\n            v-model=\"signRemark\"\r\n            placeholder=\"请输入备注\"\r\n            type=\"textarea\"\r\n            border\r\n          />\r\n        </view> -->\r\n        <u-button type=\"primary\" @tap=\"submitSign\">确认签到</u-button>\r\n      </view>\r\n    </u-popup>\r\n\r\n    <!-- 接单 -->\r\n    <u-modal\r\n      :show=\"acceptOrderModalShow\"\r\n      @confirm=\"acceptOrderConfirm\"\r\n      ref=\"uModal\"\r\n      title=\"确认接单\"\r\n      content=\"确认接单吗？\"\r\n      showCancelButton\r\n      @cancel=\"acceptOrderModalShow = false\"\r\n      :asyncClose=\"true\"\r\n    ></u-modal>\r\n    <!-- 完成 -->\r\n    <u-popup\r\n      :show=\"showFinishPopup\"\r\n      mode=\"bottom\"\r\n      @close=\"showFinishPopup = false\"\r\n      :closeable=\"true\"\r\n    >\r\n      <view class=\"finish-popup\">\r\n        <view class=\"popup-title\">完成工单</view>\r\n        <view class=\"popup-content\">\r\n          <u-form\r\n            labelPosition=\"top\"\r\n            labelWidth=\"auto\"\r\n            :model=\"finishForm\"\r\n            ref=\"finishForm\"\r\n          >\r\n            <u-form-item\r\n              borderBottom\r\n              labelPosition=\"left\"\r\n              label=\"服务开始时间\"\r\n              required\r\n            >\r\n              <view\r\n                style=\"\r\n                  display: flex;\r\n                  justify-content: flex-end;\r\n                  align-items: center;\r\n                \"\r\n              >\r\n                <text @click=\"shiwServiceStartTimePicker = true\"\r\n                  >{{\r\n                    dateFormat(\r\n                      new Date(Number(finishForm.serviceStartTime)),\r\n                      \"yyyy-MM-dd hh:mm\"\r\n                    ) || \"青选择服务开始时间\"\r\n                  }}\r\n                  <u-icon label=\"uView\" size=\"40\" name=\"arrow-right\"></u-icon\r\n                ></text>\r\n              </view>\r\n              <u-datetime-picker\r\n                v-model=\"finishForm.serviceStartTime\"\r\n                :show=\"shiwServiceStartTimePicker\"\r\n                @cancel=\"shiwServiceStartTimePicker = false\"\r\n                @confirm=\"shiwServiceStartTimePicker = false\"\r\n                mode=\"datetime\"\r\n                :visibleItemCount=\"5\"\r\n              ></u-datetime-picker>\r\n            </u-form-item>\r\n            <u-form-item\r\n              borderBottom\r\n              labelPosition=\"left\"\r\n              label=\"服务结束时间\"\r\n              required\r\n            >\r\n              <view\r\n                style=\"\r\n                  display: flex;\r\n                  justify-content: flex-end;\r\n                  align-items: center;\r\n                \"\r\n              >\r\n                <text @click=\"shiwServiceEndTimePicker = true\"\r\n                  >{{\r\n                    dateFormat(\r\n                      new Date(Number(finishForm.serviceEndTime)),\r\n                      \"yyyy-MM-dd hh:mm\"\r\n                    ) || \"青选择服务结束时间\"\r\n                  }}\r\n                  <u-icon label=\"uView\" size=\"40\" name=\"arrow-right\"></u-icon\r\n                ></text>\r\n              </view>\r\n              <u-datetime-picker\r\n                v-model=\"finishForm.serviceEndTime\"\r\n                :show=\"shiwServiceEndTimePicker\"\r\n                @cancel=\"shiwServiceEndTimePicker = false\"\r\n                @confirm=\"shiwServiceEndTimePicker = false\"\r\n                mode=\"datetime\"\r\n                :visibleItemCount=\"5\"\r\n              ></u-datetime-picker>\r\n            </u-form-item>\r\n            <!-- 实际使用工时 -->\r\n            <u-form-item labelPosition=\"left\" label=\"实际使用工时\">\r\n              <u-input\r\n                v-model=\"finishForm.useTimes\"\r\n                placeholder=\"请输入实际使用工时\"\r\n                :border=\"false\"\r\n                type=\"digit\"\r\n              ></u-input>\r\n            </u-form-item>\r\n            <u-form-item labelPosition=\"left\" label=\"完成情况\">\r\n              <dicPicker\r\n                dicUrl=\"/blade-system/dict-biz/dictionary?code=completeType\"\r\n                v-model=\"finishForm.completeStatus\"\r\n                placeholder=\"请选择完成情况\"\r\n              ></dicPicker>\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"现场图\">\r\n              <uv-upload\r\n                accept=\"media\"\r\n                @clickPreview=\"handleClickPreview\"\r\n                :fileList=\"finishForm.workOrderPhotos\"\r\n                @afterRead=\"afterReadForXC\"\r\n                @delete=\"handleDeleteForXC\"\r\n                multiple\r\n                :maxCount=\"9\"\r\n              >\r\n              </uv-upload>\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"处理结果图\">\r\n              <uv-upload\r\n                accept=\"media\"\r\n                @clickPreview=\"handleClickPreview\"\r\n                :fileList=\"finishForm.handleResultPhotos\"\r\n                @afterRead=\"afterReadForFinish\"\r\n                @delete=\"handleDeleteForFinish\"\r\n                multiple\r\n                :maxCount=\"9\"\r\n              >\r\n              </uv-upload>\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"服务复盘\">\r\n              <u-textarea\r\n                v-model=\"finishForm.serviceReorder\"\r\n                border=\"none\"\r\n                placeholder=\"请输入服务复盘\"\r\n              ></u-textarea>\r\n            </u-form-item>\r\n            <u-form-item borderBottom label=\"备注\">\r\n              <u-textarea\r\n                v-model=\"finishForm.completeRemark\"\r\n                border=\"none\"\r\n                placeholder=\"请输入备注信息\"\r\n              ></u-textarea>\r\n            </u-form-item>\r\n          </u-form>\r\n        </view>\r\n        <view class=\"popup-footer\">\r\n          <u-button type=\"primary\" @click=\"submitFinish\">提交</u-button>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport wokerOrderApi from \"@/api/wokerOrder.js\";\r\nimport QQMapWX from \"@/utils/qqmap-wx-jssdk.min.js\";\r\nimport http from \"../../http/api.js\";\r\nimport { dateFormat } from \"../../utils/date.js\";\r\nimport dicPicker from \"@/components/dic-picker/dic-picker.vue\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      showPC: false, // 视图容器控制\r\n      dateFormat,\r\n      summary: {\r\n        totalNum: 0,\r\n        waitNum: 0,\r\n        doNum: 0,\r\n        completeNum: 0,\r\n      },\r\n      tabs: [\r\n        { label: \"进行中\", value: \"processing\" },\r\n        { label: \"待接单\", value: \"pending\" },\r\n      ],\r\n      currentTab: 0,\r\n      orders: [],\r\n      statusMap: {\r\n        3: \"待接单\",\r\n        1: \"进行中\",\r\n        2: \"已完成\",\r\n      },\r\n      showSignDrawer: false,\r\n      locationLoading: false,\r\n      signAddress: null,\r\n      signPhotoUrl: \"\",\r\n      signRemark: \"\",\r\n      signOrder: null,\r\n      // 初始化地图实例\r\n      qqmapsdk: null,\r\n\r\n      // 接单\r\n      currentItem: null,\r\n      acceptOrderModalShow: false,\r\n\r\n      //完成\r\n      showFinishPopup: false,\r\n      shiwServiceStartTimePicker: false,\r\n      shiwServiceEndTimePicker: false,\r\n      finishForm: {\r\n        serviceStartTime: Number(new Date()),\r\n        serviceEndTime: Number(new Date()),\r\n        completeRemark: \"\",\r\n        useTimes: null,\r\n        serviceReorder: \"\",\r\n        workOrderPhotos: [],\r\n        handleResultPhotos: [],\r\n      },\r\n    };\r\n  },\r\n  components: {\r\n    dicPicker,\r\n  },\r\n  computed: {\r\n    filteredOrders() {\r\n      const status = this.tabs[this.currentTab].value;\r\n      return this.orders.filter((o) => o.status === status);\r\n    },\r\n  },\r\n  async onLoad() {\r\n    await this.wxlogin();\r\n    this.fetchOrders();\r\n    this.getTaskStatistics();\r\n    // 实例化，填入你申请到的 Key\r\n    this.qqmapsdk = new QQMapWX({\r\n      key: \"V37BZ-5JF63-HBZ3S-34XAJ-KPG2Q-74FPM\", // 请替换为你的真实 Key\r\n    });\r\n  },\r\n  onReachBottom() {\r\n    console.log(\"上拉加载\");\r\n    \r\n    this.fetchOrders();\r\n  },\r\n  onPullDownRefresh() {\r\n    this.fetchOrders();\r\n    this.getTaskStatistics();\r\n     uni.stopPullDownRefresh();\r\n  },\r\n  methods: {\r\n    wxlogin() {\r\n      return new Promise((resolve) => {\r\n        uni.login({\r\n          complete: (res) => {\r\n            this.$u.api\r\n              .wxToken({ code: res.code })\r\n              .then((data) => {\r\n                this.$u.func.login(data);\r\n                resolve();\r\n              })\r\n              .catch((err) => {\r\n                this.$u.func.showToast({ title: err });\r\n              });\r\n          },\r\n        });\r\n      });\r\n    },\r\n    handleClickTab(idx) {\r\n      this.currentTab = idx;\r\n      this.fetchOrders();\r\n    },\r\n    async fetchOrders() {\r\n      const map = {\r\n        0: 1,\r\n        1: 3,\r\n      };\r\n      this.$u.api\r\n        .getWorkerOrder({\r\n          pageNum: 1,\r\n          pageSize: 1000,\r\n          objectStatus: map[this.currentTab],\r\n        })\r\n        .then((data) => {\r\n          this.orders = data.data.records || [];\r\n          // this.summary = data.summary || {};\r\n        });\r\n    },\r\n    getTaskStatistics() { \r\n      this.$u.api.getTaskStatistics().then((data) => {\r\n        this.summary = data.data || {};\r\n      });\r\n    },\r\n    \r\n    //接单\r\n    handleAccept(item) {\r\n      this.currentItem = item;\r\n      this.acceptOrderModalShow = true;\r\n    },\r\n    acceptOrderConfirm() {\r\n      this.$u.api.startWorkerOrder(this.currentItem.id).then((res) => {\r\n        this.acceptOrderModalShow = false;\r\n        this.fetchOrders();\r\n      });\r\n    },\r\n    handleSign(order) {\r\n      this.signOrder = order;\r\n      this.showSignDrawer = true;\r\n      this.signAddress = null;\r\n      this.signPhotoUrl = [];\r\n      this.signRemark = \"\";\r\n      this.chooseLocation();\r\n    },\r\n    chooseLocation() {\r\n      // wx.chooseLocation({\r\n      //   success: (res) => {\r\n      //     this.signAddress = res;\r\n      //   },\r\n      //   fail: (err) => {\r\n      // \tconsole.log(err);\r\n\r\n      //     uni.showToast({ title: '位置选择失败', icon: 'none' });\r\n      //   }\r\n      // });\r\n      this.locationLoading = true;\r\n      wx.getLocation({\r\n        type: \"gcj02\",\r\n\r\n        success: (res) => {\r\n          console.log(res);\r\n\r\n          // 成功获取经纬度后，进行逆地址解析\r\n          this.qqmapsdk.reverseGeocoder({\r\n            location: {\r\n              latitude: res.latitude,\r\n              longitude: res.longitude,\r\n            },\r\n            success: (result) => {\r\n              // 逆解析成功回调\r\n              console.log(\"逆地址解析结果：\", result);\r\n              // 详细的地址信息在 result.result 里\r\n              const addressInfo = result.result.address_component;\r\n              const formattedAddress = result.result.address;\r\n              console.log(\"所在城市：\", addressInfo.city);\r\n              console.log(\"完整地址：\", formattedAddress);\r\n              this.locationLoading = false;\r\n              this.signAddress = formattedAddress;\r\n              // 你可以在这里将地址信息更新到 data 中，或进行其他操作\r\n            },\r\n            fail: function (err) {\r\n              this.locationLoading = false;\r\n              console.error(\"逆地址解析失败：\", err);\r\n              uni.showToast({ title: \"位置解析失败\", icon: \"none\" });\r\n            },\r\n          });\r\n        },\r\n        fail: (err) => {\r\n          console.log(err);\r\n          this.locationLoading = false;\r\n          uni.showToast({ title: \"位置选择失败\", icon: \"none\" });\r\n        },\r\n      });\r\n    },\r\n    afterReadSign(event) {\r\n      console.log(event);\r\n      const { file } = event;\r\n      const indexAll = this.signPhotoUrl.length;\r\n      file.forEach((item, index) => {\r\n        this.signPhotoUrl.push({\r\n          ...item,\r\n          status: \"uploading\",\r\n          message: \"上传中\",\r\n          // url: item.thumb,\r\n          index: indexAll + index,\r\n        });\r\n      });\r\n      file.forEach((item, index) => {\r\n        this.uploadFileSign(item.url, indexAll + index);\r\n      });\r\n    },\r\n    uploadFileSign(url, index) {\r\n      return new Promise((resolve) => {\r\n        const params = {\r\n          filePath: url,\r\n          name: \"file\",\r\n        };\r\n        http.upload(\"/blade-resource/attach/upload\", params).then((res) => {\r\n          this.signPhotoUrl.find((item) => item.index == index).status = \"success\";\r\n          this.signPhotoUrl.find((item) => item.index == index).message = \"\";\r\n          this.signPhotoUrl.find((item) => item.index == index).url =\r\n            res.data.link;\r\n        });\r\n      });\r\n    },\r\n    handleDeleteSign({ file, index, name }) {\r\n      console.log(file, index, name);\r\n      this.signPhotoUrl.splice(index, 1);\r\n    },\r\n    handleClickPreview(url, lists, name) {\r\n      console.log(url, lists, name);\r\n    },\r\n    submitSign() {\r\n      // if (!this.signAddress) {\r\n      //   uni.showToast({ title: \"请获取位置\", icon: \"none\" });\r\n      //   return;\r\n      // }\r\n      if (!this.signPhotoUrl) {\r\n        uni.showToast({ title: \"请上传照片\", icon: \"none\" });\r\n        return;\r\n      }\r\n      // 这里可以提交签到数据\r\n      const data = {\r\n        id: this.signOrder.id,\r\n        address: this.signAddress,\r\n        signPhotoUrl: this.signPhotoUrl.map((item) => item.url).join(\",\"),\r\n        // remark: this.signRemark,\r\n      };\r\n      this.$u.api\r\n        .signIn(data)\r\n        .then((res) => {\r\n          console.log(res);\r\n        })\r\n        .then(() => {\r\n          uni.showToast({ title: \"签到成功\", icon: \"success\" });\r\n          this.showSignDrawer = false;\r\n          this.fetchOrders();\r\n        });\r\n    },\r\n\r\n    handleFinish(item) {\r\n      this.showFinishPopup = true;\r\n      this.showPC = true;\r\n      this.currentItem = item;\r\n      this.finishForm.serviceStartTime = Number(new Date(item.serviceStartTime));\r\n      this.finishForm.serviceEndTime = Number(new Date(item.serviceEndTime));\r\n    },\r\n    afterReadForXC(event) {\r\n      console.log(event);\r\n      const { file } = event;\r\n      const indexAll = this.finishForm.workOrderPhotos.length;\r\n      file.forEach((item, index) => {\r\n        this.finishForm.workOrderPhotos.push({\r\n          ...item,\r\n          status: \"uploading\",\r\n          message: \"上传中\",\r\n          // url: item.thumb,\r\n          index: indexAll + index,\r\n        });\r\n      });\r\n      file.forEach((item, index) => {\r\n        this.uploadFileForXC(item.url, indexAll + index);\r\n      });\r\n    },\r\n    uploadFileForXC(url, index) {\r\n      return new Promise((resolve) => {\r\n        const params = {\r\n          filePath: url,\r\n          name: \"file\",\r\n        };\r\n        http.upload(\"/blade-resource/attach/upload\", params).then((res) => {\r\n          this.finishForm.workOrderPhotos.find(\r\n            (item) => item.index == index\r\n          ).status = \"success\";\r\n          this.finishForm.workOrderPhotos.find(\r\n            (item) => item.index == index\r\n          ).message = \"\";\r\n          this.finishForm.workOrderPhotos.find(\r\n            (item) => item.index == index\r\n          ).url = res.data.link;\r\n          this.finishForm.workOrderPhotos.find(\r\n            (item) => item.index == index\r\n          ).id = res.data.id;\r\n        });\r\n      });\r\n    },\r\n    handleDeleteForXC({ file, index, name }) {\r\n      console.log(file, index, name);\r\n      this.finishForm.workOrderPhotos.splice(index, 1);\r\n    },\r\n\r\n    afterReadForFinish(event) {\r\n      console.log(event);\r\n      const { file } = event;\r\n      const indexAll = this.finishForm.handleResultPhotos.length;\r\n      file.forEach((item, index) => {\r\n        this.finishForm.handleResultPhotos.push({\r\n          ...item,\r\n          status: \"uploading\",\r\n          message: \"上传中\",\r\n          // url: item.thumb,\r\n          index: indexAll + index,\r\n        });\r\n      });\r\n      file.forEach((item, index) => {\r\n        this.uploadFileForFinish(item.url, indexAll + index);\r\n      });\r\n    },\r\n    uploadFileForFinish(url, index) {\r\n      return new Promise((resolve) => {\r\n        const params = {\r\n          filePath: url,\r\n          name: \"file\",\r\n        };\r\n        http.upload(\"/blade-resource/attach/upload\", params).then((res) => {\r\n          this.finishForm.handleResultPhotos.find(\r\n            (item) => item.index == index\r\n          ).status = \"success\";\r\n          this.finishForm.handleResultPhotos.find(\r\n            (item) => item.index == index\r\n          ).message = \"\";\r\n          this.finishForm.handleResultPhotos.find(\r\n            (item) => item.index == index\r\n          ).url = res.data.link;\r\n          this.finishForm.handleResultPhotos.find(\r\n            (item) => item.index == index\r\n          ).id = res.data.id;\r\n        });\r\n      });\r\n    },\r\n    handleDeleteForFinish({ file, index, name }) {\r\n      console.log(file, index, name);\r\n      this.finishForm.handleResultPhotos.splice(index, 1);\r\n    },\r\n\r\n    submitFinish() {\r\n      if (!this.finishForm.serviceStartTime || !this.finishForm.serviceEndTime) {\r\n        this.$u.toast(\"开始时间和结束时间不能为空\");\r\n        return;\r\n      }\r\n      const formData = {\r\n        id: this.currentItem.id,\r\n        serviceStartTime: this.dateFormat(\r\n          new Date(Number(this.finishForm.serviceStartTime)),\r\n          \"yyyy-MM-dd hh:mm:ss\"\r\n        ),\r\n        serviceEndTime: this.dateFormat(\r\n          new Date(Number(this.finishForm.serviceEndTime)),\r\n          \"yyyy-MM-dd hh:mm:ss\"\r\n        ),\r\n        completeRemark: this.finishForm.completeRemark,\r\n        // completeFiles:\r\n        //   this.finishForm.fileList &&\r\n        //   this.finishForm.fileList.map((item) => item.id).join(\",\"),\r\n        handleResultPhotos:\r\n          this.finishForm.handleResultPhotos &&\r\n          this.finishForm.handleResultPhotos.map((item) => item.id).join(\",\"),\r\n        workOrderPhotos:\r\n          this.finishForm.workOrderPhotos &&\r\n          this.finishForm.workOrderPhotos.map((item) => item.id).join(\",\"),\r\n        useTimes: this.finishForm.useTimes,\r\n        completeStatus: this.finishForm.completeStatus,\r\n        serviceReorder: this.finishForm.serviceReorder,\r\n      };\r\n      this.$u.api\r\n        .finishWorkerOrder(formData)\r\n        .then((res) => {\r\n          this.$u.toast(\"提交成功\");\r\n          this.showFinishPopup = false;\r\n          this.finishForm = {\r\n            finishTime: \"\",\r\n            completeRemark: \"\",\r\n            useTimes: null,\r\n            serviceReorder: \"\",\r\n            workOrderPhotos: [],\r\n            handleResultPhotos: [],\r\n          };\r\n          this.fetchOrders();\r\n        })\r\n        .catch((err) => {\r\n          this.$u.toast(err.message || \"提交失败\");\r\n        });\r\n    },\r\n    toDetail(item) {\r\n      uni.navigateTo({\r\n        url: \"/pages/wokerOrder/wokerOrderDetail?id=\" + item.id,\r\n      });\r\n    },\r\n    makePhoneCall(phone) {\r\n      uni.makePhoneCall({\r\n        phoneNumber: phone,\r\n        success() {\r\n          console.log(\"拨打电话成功！\");\r\n        },\r\n        fail(err) {\r\n          console.log(\"拨打电话失败！\", err);\r\n        },\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.workbench-wrap {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  padding: 32rpx 0 0 0;\r\n  box-sizing: border-box;\r\n}\r\n.summary-card {\r\n  margin: 32rpx 32rpx 24rpx 32rpx;\r\n  background: #fff;\r\n  border-radius: 24rpx;\r\n  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.06);\r\n  padding: 32rpx 24rpx 24rpx 24rpx;\r\n}\r\n.summary-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 24rpx;\r\n  color: #333;\r\n}\r\n.summary-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n.stat-item {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n.stat-label {\r\n  font-size: 24rpx;\r\n  color: #888;\r\n  margin-bottom: 8rpx;\r\n}\r\n.stat-value {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #2d8cf0;\r\n}\r\n.stat-item.total .stat-value {\r\n  color: #2d8cf0;\r\n}\r\n.stat-item.pending .stat-value {\r\n  color: #ff9900;\r\n}\r\n.stat-item.processing .stat-value {\r\n  color: #19be6b;\r\n}\r\n.stat-item.finished .stat-value {\r\n  color: #909399;\r\n}\r\n\r\n.order-tab-bar {\r\n  display: flex;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  margin: 0 32rpx 16rpx 32rpx;\r\n  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);\r\n  overflow: hidden;\r\n}\r\n.tab-item {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 24rpx 0;\r\n  font-size: 28rpx;\r\n  color: #888;\r\n  background: #fff;\r\n  transition: all 0.2s;\r\n}\r\n.tab-item.active {\r\n  color: #2d8cf0;\r\n  font-weight: bold;\r\n  background: #e6f7ff;\r\n}\r\n\r\n.order-list {\r\n  margin: 0 32rpx;\r\n}\r\n.order-card {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.04);\r\n  margin-bottom: 24rpx;\r\n  padding: 24rpx 20rpx 16rpx 20rpx;\r\n  transition: box-shadow 0.2s;\r\n}\r\n.order-actions {\r\n  display: flex;\r\n  gap: 24rpx;\r\n  border-top: 1px solid #f0f0f0;\r\n  margin-top: 18rpx;\r\n  padding-top: 18rpx;\r\n  justify-content: flex-end;\r\n  background: #fff;\r\n}\r\n.action-btn {\r\n  min-width: 120rpx;\r\n  padding: 0 32rpx;\r\n  height: 56rpx;\r\n\r\n  line-height: 56rpx;\r\n  border: none;\r\n  border-radius: 32rpx;\r\n  background: #f5f7fa;\r\n  color: #2d8cf0;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  margin: 0;\r\n  outline: none;\r\n  box-shadow: 0 2rpx 8rpx 0 rgba(45, 140, 240, 0.04);\r\n  transition: background 0.2s, color 0.2s;\r\n}\r\n.action-btn.primary {\r\n  background: linear-gradient(90deg, #2d8cf0 0%, #57a3f3 100%);\r\n  color: #fff;\r\n}\r\n.action-btn:active {\r\n  opacity: 0.85;\r\n}\r\n.order-card:hover {\r\n  box-shadow: 0 8rpx 32rpx 0 rgba(45, 140, 240, 0.08);\r\n}\r\n.order-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12rpx;\r\n}\r\n.order-title {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n.order-status {\r\n  font-size: 24rpx;\r\n  padding: 4rpx 16rpx;\r\n  border-radius: 16rpx;\r\n  background: #f5f7fa;\r\n}\r\n.order-status.status-needsign {\r\n  color: #fff;\r\n  background: #ff9900;\r\n}\r\n.order-status.status-1 {\r\n  color: #19be6b;\r\n  background: #e6ffed;\r\n}\r\n.order-status.status-2 {\r\n  color: #909399;\r\n  background: #f4f4f5;\r\n}\r\n.order-detail-row {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  font-size: 26rpx;\r\n  color: #555;\r\n  margin-bottom: 8rpx;\r\n}\r\n.order-label {\r\n  min-width: 120rpx;\r\n  color: #888;\r\n  font-weight: 400;\r\n}\r\n.order-value {\r\n  flex: 1;\r\n  color: #333;\r\n  word-break: break-all;\r\n}\r\n.empty-tip {\r\n  text-align: center;\r\n  color: #bbb;\r\n  font-size: 28rpx;\r\n  margin: 64rpx 0;\r\n}\r\n.finish-popup {\r\n  height: 80vh;\r\n  overflow-y: auto;\r\n  position: relative;\r\n  .popup-title {\r\n    padding-top: 20rpx;\r\n    position: sticky;\r\n    background-color: #fff;\r\n    top: 0;\r\n    z-index: 10;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    text-align: center;\r\n    // margin-bottom: 30rpx;\r\n  }\r\n\r\n  .popup-content {\r\n    padding: 30rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n\r\n  .popup-footer {\r\n    padding: 20rpx 0;\r\n    position: fixed;\r\n    bottom: 0;\r\n    left: 20rpx;\r\n    right: 20rpx;\r\n    width: auto;\r\n  }\r\n}\r\n::v-deep input {\r\n  text-align: right !important;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1759057171365\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}